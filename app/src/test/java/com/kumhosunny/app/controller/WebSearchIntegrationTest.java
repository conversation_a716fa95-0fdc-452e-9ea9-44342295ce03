package com.kumhosunny.app.controller;

import com.kumhosunny.chat.dto.ChatCompletionRequest;
import com.kumhosunny.chat.dto.ChatMessage;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 联网搜索集成测试
 * 测试ModelProxyController中的联网搜索功能
 */
@SpringBootTest
@TestPropertySource(properties = {
    "tavily.api.key=test-key",
    "tavily.api.url=https://api.tavily.com"
})
public class WebSearchIntegrationTest {

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
    }

    @Test
    void testChatCompletionRequestWithWebSearch() {
        // 创建包含联网搜索的请求
        ChatCompletionRequest request = new ChatCompletionRequest();
        request.setModel("gpt-3.5-turbo");
        request.setWebSearchEnabled(true);
        
        List<ChatMessage> messages = new ArrayList<>();
        messages.add(new ChatMessage("user", "What is the latest news about artificial intelligence?"));
        request.setMessages(messages);
        
        // 验证请求结构
        assertNotNull(request);
        assertTrue(request.getWebSearchEnabled());
        assertEquals(1, request.getMessages().size());
        assertEquals("user", request.getMessages().get(0).getRole());
    }

    @Test
    void testChatCompletionRequestWithoutWebSearch() {
        // 创建不包含联网搜索的请求
        ChatCompletionRequest request = new ChatCompletionRequest();
        request.setModel("gpt-3.5-turbo");
        request.setWebSearchEnabled(false);
        
        List<ChatMessage> messages = new ArrayList<>();
        messages.add(new ChatMessage("user", "Hello, how are you?"));
        request.setMessages(messages);
        
        // 验证请求结构
        assertNotNull(request);
        assertFalse(request.getWebSearchEnabled());
        assertEquals(1, request.getMessages().size());
    }

    @Test
    void testChatCompletionRequestWebSearchNull() {
        // 测试webSearchEnabled为null的情况
        ChatCompletionRequest request = new ChatCompletionRequest();
        request.setModel("gpt-3.5-turbo");
        // webSearchEnabled默认为null
        
        List<ChatMessage> messages = new ArrayList<>();
        messages.add(new ChatMessage("user", "Test message"));
        request.setMessages(messages);
        
        // 验证请求结构
        assertNotNull(request);
        assertNull(request.getWebSearchEnabled());
        assertEquals(1, request.getMessages().size());
    }

    @Test
    void testRequestSerialization() throws Exception {
        // 测试请求的JSON序列化
        ChatCompletionRequest request = new ChatCompletionRequest();
        request.setModel("gpt-3.5-turbo");
        request.setWebSearchEnabled(true);
        request.setKnowledgeEnabled(false);
        
        List<ChatMessage> messages = new ArrayList<>();
        messages.add(new ChatMessage("user", "Search for latest AI developments"));
        request.setMessages(messages);
        
        // 序列化为JSON
        String json = objectMapper.writeValueAsString(request);
        assertNotNull(json);
        assertTrue(json.contains("webSearchEnabled"));
        assertTrue(json.contains("true"));
        
        // 反序列化
        ChatCompletionRequest deserializedRequest = objectMapper.readValue(json, ChatCompletionRequest.class);
        assertNotNull(deserializedRequest);
        assertTrue(deserializedRequest.getWebSearchEnabled());
        assertEquals("gpt-3.5-turbo", deserializedRequest.getModel());
        assertEquals(1, deserializedRequest.getMessages().size());
    }

    @Test
    void testMultipleMessagesWithWebSearch() {
        // 测试多条消息的情况
        ChatCompletionRequest request = new ChatCompletionRequest();
        request.setModel("gpt-3.5-turbo");
        request.setWebSearchEnabled(true);
        
        List<ChatMessage> messages = new ArrayList<>();
        messages.add(new ChatMessage("system", "You are a helpful assistant."));
        messages.add(new ChatMessage("user", "What's the weather like today?"));
        messages.add(new ChatMessage("assistant", "I need to search for current weather information."));
        messages.add(new ChatMessage("user", "Please search for weather in Beijing"));
        request.setMessages(messages);
        
        // 验证请求结构
        assertNotNull(request);
        assertTrue(request.getWebSearchEnabled());
        assertEquals(4, request.getMessages().size());
        
        // 验证最后一条用户消息
        ChatMessage lastUserMessage = null;
        for (int i = messages.size() - 1; i >= 0; i--) {
            if ("user".equals(messages.get(i).getRole())) {
                lastUserMessage = messages.get(i);
                break;
            }
        }
        
        assertNotNull(lastUserMessage);
        assertEquals("Please search for weather in Beijing", lastUserMessage.getContent());
    }

    /**
     * 模拟测试方法，展示如何使用联网搜索功能
     */
    @Test
    void testWebSearchUsageExample() {
        // 这是一个使用示例，展示如何构建包含联网搜索的请求
        
        // 1. 创建请求
        ChatCompletionRequest request = new ChatCompletionRequest();
        request.setModel("deepseek-ai/DeepSeek-V3");
        request.setWebSearchEnabled(true);  // 启用联网搜索
        request.setMaxTokens(1000);
        request.setTemperature(0.7);
        
        // 2. 添加消息
        List<ChatMessage> messages = new ArrayList<>();
        messages.add(new ChatMessage("user", "What are the latest developments in quantum computing in 2024?"));
        request.setMessages(messages);
        
        // 3. 验证请求配置
        assertTrue(request.getWebSearchEnabled());
        assertEquals("deepseek-ai/DeepSeek-V3", request.getModel());
        assertEquals(1000, request.getMaxTokens());
        assertEquals(0.7, request.getTemperature());
        
        // 4. 验证消息内容
        assertEquals(1, request.getMessages().size());
        ChatMessage userMessage = request.getMessages().get(0);
        assertEquals("user", userMessage.getRole());
        assertTrue(userMessage.getContent().toString().contains("quantum computing"));
        
        System.out.println("Web search request created successfully:");
        System.out.println("Model: " + request.getModel());
        System.out.println("Web Search Enabled: " + request.getWebSearchEnabled());
        System.out.println("User Query: " + userMessage.getContent());
    }
}
