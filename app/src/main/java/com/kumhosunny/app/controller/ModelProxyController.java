package com.kumhosunny.app.controller;

import com.kumhosunny.app.ai.agent.AgentFactory;
import com.kumhosunny.app.ai.agent.ChatCompletionAgent;
import com.kumhosunny.chat.dto.ChatCompletionRequest;
import com.kumhosunny.chat.dto.ChatCompletionResponse;
import com.kumhosunny.chat.dto.ChatMessage;
import com.kumhosunny.chat.dto.ModelsResponse;
import com.kumhosunny.chat.dto.ModelRouteResponse;
import com.kumhosunny.chat.dto.ImageGenerationRequest;
import com.kumhosunny.chat.dto.ImageGenerationResponse;
import com.kumhosunny.chat.service.ModelRouterService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kumhosunny.knowledge.plugins.KnowledgePlugin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.kumhosunny.chat.service.ChatSessionService;
import com.kumhosunny.common.entity.ChatMessage.MessageSender;
import com.kumhosunny.common.entity.ChatSession;
import com.kumhosunny.common.util.UserContextUtil;
import javax.servlet.http.HttpServletRequest;
import com.kumhosunny.chat.dto.SaveMessageRequest;
import com.kumhosunny.chat.dto.ChatCompletionChunk;
import com.fasterxml.jackson.core.type.TypeReference;
import com.kumhosunny.chat.service.AiAppService;
import com.kumhosunny.common.entity.AiAppSettings;
import com.kumhosunny.common.entity.AiApp;
import com.kumhosunny.chat.dto.CreateSessionRequest;
import com.kumhosunny.common.entity.AiAppTools;
import com.kumhosunny.tools.plugins.N8nPlugin;
import com.kumhosunny.tools.plugins.TavilyPlugin;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.io.IOException;
import java.util.ArrayList;

import com.kumhosunny.common.repository.EmployeeRepository;
import com.kumhosunny.common.repository.AiModelRouteRepository;
import com.kumhosunny.common.entity.EmployeeEntity;
import com.kumhosunny.common.entity.AiModelRoute;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import java.util.stream.Collectors;
import java.util.HashMap;

/**
 * AI 模型代理控制器 (App 模块版本)
 * 提供统一的 OpenAI 兼容 API 接口，结合 Semantic Kernel Agent 功能
 * 
 * 架构设计:
 * [前端 UI / 客户端]
 * ↓
 * [OpenAI API 兼容接口]
 * ↓
 * ┌─────────────────────────────────────┐
 * │ 智能路由器 │
 * │ - 传统模型 → ModelRouterService │
 * │ - SK Agent → 统一Agent + 自动插件调用 │
 * └─────────────────────────────────────┘
 * 
 * Semantic Kernel 设计理念：
 * - 使用统一的 ChatCompletionAgent
 * - AI 根据用户输入自动选择和调用合适的插件
 * - 无需手动判断 Agent 类型
 */
@RestController
@RequestMapping("/v1")
@CrossOrigin(origins = "*")
public class ModelProxyController {

    private static final Logger log = LoggerFactory.getLogger(ModelProxyController.class);

    @Autowired
    private ModelRouterService modelRouterService;

    @Autowired
    private AgentFactory agentFactory;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ChatSessionService chatSessionService;

    @Autowired
    private AiAppService aiAppService;

    @Autowired(required = false) // 如果 tools 模块没有被包含，允许为 null
    private N8nPlugin n8nPlugin;

    @Autowired(required = false) // 如果 tools 模块没有被包含，允许为 null
    private KnowledgePlugin knowledgePlugin;

    @Autowired(required = false) // 如果 tools 模块没有被包含，允许为 null
    private TavilyPlugin tavilyPlugin;

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private AiModelRouteRepository aiModelRouteRepository;

    @Autowired
    private UserContextUtil userContextUtil;

    /**
     * 聊天完成 API
     * 兼容 OpenAI chat/completions 接口
     * 
     * 核心逻辑:
     * 1. 获取或创建用户会话。
     * 2. 从会话的 `metadata` 中提取应用配置（如 `appId`）。
     * 3. 如果是应用（App）调用，根据 `appId` 获取应用的设置（如系统提示词、特定模型）。
     * 4. 将系统提示词注入到请求的消息列表中。
     * 5. 根据配置（或默认值）确定要使用的模型。
     * 6. 根据是否为流式（stream）请求和是否使用 Agent，将请求路由到相应的处理逻辑。
     * 7. 保存用户和助手的消息，并在新会话中生成标题。
     */
    @PostMapping("/chat/completions")
    public Object chatCompletions(@RequestBody ChatCompletionRequest request, HttpServletRequest httpRequest) {
        Long userId = userContextUtil.getCurrentUserId(httpRequest);
        ChatSession activeSession = getOrCreateActiveSession(userId, request);

        // 注意：这里的 saveUserMessage 可能会保存包含JSON的content
        // 而解析和替换将在 processAttachmentsAndKnowledgeSearch 中进行
        saveUserMessage(request, activeSession.getId(), userId);

        // 新增：处理附件和知识库查询
        processAttachmentsAndKnowledgeSearch(request, userId);

        // 新增：处理联网搜索
        processWebSearch(request, userId);

        // 从会话元数据处理应用逻辑
        processAppLogic(request, activeSession, userId);

        String model = request.getModel();
        Boolean stream = request.getStream();

        log.info("Chat completions request: model={}, stream={}", model, stream);

        // 根据请求中的 tools 字段判断是否使用 Semantic Kernel Agent
        if (shouldUseSemanticKernelAgent(request)) {
            return handleSemanticKernelRequest(request, activeSession.getId(), userId);
        }

        // 使用传统模型路由
        if (Boolean.TRUE.equals(stream)) {
            return handleStreamRequest(request, activeSession.getId(), userId);
        }

        return modelRouterService.routeAndProcess(request)
                .doOnSuccess(response -> saveAssistantMessage(response, activeSession.getId(), userId));
    }

    /**
     * 从会话元数据处理应用相关逻辑
     * <p>
     * 1. 解析会话中的 `metadata` JSON 字符串。
     * 2. 如果元数据中包含 `app` 信息（特别是 `appId`），则获取该应用的设置。
     * 3. 应用设置 (`AiAppSettings`) 可能包含自定义的系统提示 (`systemPrompt`) 和指定的模型 (`model`)。
     * 4. 将系统提示词作为第一条消息注入到请求中，以指导模型的行为。
     * 5. 如果设置中指定了模型，则使用该模型；否则，使用默认模型。
     *
     * @param request       聊天请求对象，将被修改（注入系统提示词，设置模型）
     * @param activeSession 当前用户的活动会话
     * @param userId        当前用户的ID
     */
    private void processAppLogic(ChatCompletionRequest request, ChatSession activeSession, Long userId) {
        String metadataJson = activeSession.getMetadata();
        if (metadataJson == null || metadataJson.isEmpty()) {
            return;
        }

        try {
            Map<String, Object> metadata = objectMapper.readValue(metadataJson, new TypeReference<>() {
            });
            @SuppressWarnings("unchecked")
            Map<String, Object> appInfo = (Map<String, Object>) metadata.get("app");

            if (appInfo != null && appInfo.get("id") != null) {
                Integer appId = ((Number) appInfo.get("id")).intValue();
                AiAppSettings appSettings = aiAppService.getAppSettings(appId);
                List<AiAppTools> appTools = aiAppService.getAppTools(appId);

                // 执行工具并获取结果
                String toolResult = executeAppTools(request, appTools, userId);

                // 1. 获取基础系统提示词 (AI的角色和任务指令)
                String systemPrompt = "";
                if (appSettings != null && appSettings.getSystemPrompt() != null
                        && !appSettings.getSystemPrompt().isEmpty()) {
                    systemPrompt = appSettings.getSystemPrompt();
                }

                // 2. 如果有工具执行结果，将其附加到提示词中
                if (toolResult != null && !toolResult.isEmpty()) {
                    // 如果基础提示词为空，使用您提供的默认指令
                    if (systemPrompt.isEmpty()) {
                        systemPrompt = "你是一名智能助手，用户将向你提问。我们会在你回答之前，通过工具调用获取某些实时数据（如天气、汇率、执行结果等），这些数据会以补充内容的形式附加在你的提示末尾。\n\n"
                                +
                                "你的任务是：\n" +
                                "- 仅参考这些数据来辅助回答用户的问题；\n" +
                                "- 不要提及你是通过工具或 API 获取的数据；\n" +
                                "- 不要透露任何关于工具调用、接口、技术细节等内容；\n" +
                                "- 用自然语言、直接的方式回答用户，就像你知道这些信息一样。\n\n" +
                                "请牢记：**用户只关心最终的答案，不需要知道你怎么得到的。**";
                    }
                    // 按照指定格式附加外部数据
                    systemPrompt += "\n\n【外部数据】：\n" + toolResult;
                }

                // 3. 注入最终的系统提示词（如果存在）
                if (!systemPrompt.isEmpty()) {
                    ChatMessage systemMessage = new ChatMessage("system", systemPrompt.trim());
                    List<ChatMessage> messages = new ArrayList<>();
                    messages.add(systemMessage);
                    messages.addAll(request.getMessages());
                    request.setMessages(messages);
                }

                // 4. 设置模型
                if (appSettings != null) {
                    String modelFromSettings = appSettings.getModel();
                    if (modelFromSettings != null && !modelFromSettings.isEmpty()) {
                        request.setModel(modelFromSettings);
                        log.info("Using model from app settings: {}", modelFromSettings);
                    } else {
                        request.setModel("deepseek-ai/DeepSeek-V3"); // 默认模型
                        log.info("App settings did not specify a model, using default: deepseek-chat");
                    }
                }
            }
        } catch (IOException e) {
            log.error("Failed to parse metadata JSON from session {}: {}", activeSession.getId(), metadataJson, e);
        }
    }

    /**
     * 执行与应用关联的工具
     *
     * @param request  当前的聊天请求，用于获取输入数据
     * @param appTools 应用关联的工具列表
     * @param userId   当前用户的ID
     * @return 所有工具执行结果的汇总字符串，如果无工具执行则返回 null
     */
    private String executeAppTools(ChatCompletionRequest request, List<AiAppTools> appTools, Long userId) {
        if (appTools == null || appTools.isEmpty() || n8nPlugin == null) {
            return null;
        }

        // 1. 获取用户信息
        String username = employeeRepository.findById(userId)
                .map(EmployeeEntity::getLoginName)
                .orElse(String.valueOf(userId)); // 如果找不到，用userId作为后备

        StringBuilder results = new StringBuilder();
        String lastUserMessage = getLastUserMessage(request);

        for (AiAppTools tool : appTools) {
            if ("n8n".equalsIgnoreCase(tool.getToolName())) {
                try {
                    String configJson = tool.getConfig();
                    if (configJson != null && !configJson.isEmpty()) {

                        // 新增：解析JSON并提取 workflowId
                        String workflowId = configJson; // 默认为整个 JSON，以防解析失败
                        String webhook = null;
                        try {
                            JsonNode configNode = objectMapper.readTree(configJson);
                            if (configNode.has("n8n_workflow_id")) {
                                workflowId = configNode.get("n8n_workflow_id").asText();
                            }
                            webhook = configNode.get("n8n_req_address").asText();
                        } catch (IOException e) {
                            log.error("Failed to parse n8n config JSON, fallback to use raw config. JSON: {}",
                                    configJson, e);
                        }

                        log.info("Executing n8n tool with workflowId: {} for user: {}", workflowId, username);
                        // 修改：调用 n8n 方法时，传入提取出的 workflowId
                        String result = n8nPlugin.callWorkflow(workflowId, lastUserMessage, username, webhook);
                        results.append(result).append("\n\n");
                    }
                } catch (Exception e) {
                    log.error("Failed to execute n8n tool (id: {}): {}", tool.getId(), e.getMessage(), e);
                    results.append("n8n 工具执行失败: ").append(e.getMessage()).append("\n\n");
                }
            }
            // 未来可以 在这里添加其他工具类型 (e.g., 'code_sandbox', 'search')
            // if ("knowledge".equalsIgnoreCase(tool.getToolName())) {
            // String result = knowledgePlugin.searchKnowledge(lastUserMessage, userId,
            // request.getKnowledgeType());
            // results.append(result).append("\n\n");
            // }
        }

        return results.length() > 0 ? results.toString().trim() : null;
    }

    /**
     * 从请求中获取最后一条用户消息的内容
     */
    private String getLastUserMessage(ChatCompletionRequest request) {
        List<ChatMessage> messages = request.getMessages();
        if (messages != null && !messages.isEmpty()) {
            for (int i = messages.size() - 1; i >= 0; i--) {
                ChatMessage message = messages.get(i);
                if ("user".equalsIgnoreCase(message.getRole())) {
                    // 对于用户消息，content 可能是 JSON，也可能是纯文本
                    // processAttachmentsAndKnowledgeSearch 已经将JSON解析并替换为纯文本
                    // 所以这里直接返回 content 即可
                    return message.getContent().toString();
                }
            }
        }
        return ""; // 如果没有找到用户消息，返回空字符串
    }

    private ChatSession getOrCreateActiveSession(Long userId, ChatCompletionRequest request) {
        List<ChatSession> activeSessions = chatSessionService.getActiveSessions(userId);
        if (activeSessions.isEmpty()) {
            CreateSessionRequest createRequest = new CreateSessionRequest();
            createRequest.setSessionTitle("新对话");
            createRequest.setModelUsed(request.getModel());
            // 如果请求中包含 appId，创建会话时可以将其存入 metadata
            if (request.getAppId() != null) {
                try {
                    // 根据appId查询app名称
                    AiApp app = aiAppService.getAppById(Integer.parseInt(request.getAppId()));
                    String appName = (app != null) ? app.getName() : "未知应用";

                    Map<String, Object> appInfo = Map.of("app", Map.of("id", request.getAppId(), "name", appName));
                    createRequest.setMetadata(objectMapper.writeValueAsString(appInfo));
                } catch (Exception e) {
                    log.error("Failed to serialize appId to metadata", e);
                }
            }
            return chatSessionService.createSession(createRequest, userId);
        }
        return activeSessions.get(0);
    }

    private void saveUserMessage(ChatCompletionRequest request, String sessionId, Long userId) {
        // 保存用户消息
        List<com.kumhosunny.chat.dto.ChatMessage> messages = request.getMessages();
        if (messages != null && !messages.isEmpty()) {
            // 获取原始的最后一条消息来保存
            com.kumhosunny.chat.dto.ChatMessage lastMessage = messages.get(messages.size() - 1);
            if ("user".equalsIgnoreCase(lastMessage.getRole())) {
                SaveMessageRequest saveMessageRequest = new SaveMessageRequest();
                saveMessageRequest.setSessionId(sessionId);
                saveMessageRequest.setSender(MessageSender.user);

                // 直接保存原始 content，它可能是JSON或纯文本
                // 不再依赖于处理后的纯文本，因为 processAttachmentsAndKnowledgeSearch 发生在此之后
                saveMessageRequest.setContent(lastMessage.getContent().toString());
                chatSessionService.saveMessage(saveMessageRequest, userId);
            }
        }
    }

    private void saveAssistantMessage(ChatCompletionResponse response, String sessionId, Long userId) {
        if (response != null && response.getChoices() != null && !response.getChoices().isEmpty()) {
            ChatCompletionResponse.Choice choice = response.getChoices().get(0);
            if (choice.getMessage() != null && choice.getMessage().getContent() != null) {
                saveAssistantMessage(choice.getMessage().getContent().toString(), sessionId, userId, true);
            }
        }
    }

    /**
     * 判断是否应该使用 Semantic Kernel Agent
     * 根据 tools 字段的内容是否包含 "agent" 来判断
     */
    private boolean shouldUseSemanticKernelAgent(ChatCompletionRequest request) {
        List<Map<String, Object>> tools = request.getTools();
        if (tools == null || tools.isEmpty()) {
            return false;
        }

        // 检查 tools 中是否包含包含 "agent" 的工具
        return tools.size() > 0;
    }

    /**
     * 处理 Semantic Kernel Agent 请求
     * 使用统一的 Agent，让 SK 框架自动决定插件调用
     */
    private Object handleSemanticKernelRequest(ChatCompletionRequest request, String sessionId, Long userId) {
        try {
            String model = request.getModel();
            String actualModel = extractActualModel(model);

            log.info("Using Semantic Kernel Agent with model: {}", actualModel);

            // 创建统一的智能助手 Agent
            // SK 框架会根据用户输入自动决定调用哪些插件
            ChatCompletionAgent agent = agentFactory.createGeneralAssistant(actualModel);

            if (agent == null) {
                log.error("Failed to create Semantic Kernel Agent");
                return Mono.just(createErrorResponse("Failed to create agent"));
            }

            // 构建完整的对话历史
            String conversationHistory = buildConversationHistory(request);

            Boolean stream = request.getStream();
            if (Boolean.TRUE.equals(stream)) {
                return handleAgentStreamRequest(agent, conversationHistory, sessionId, userId);
            } else {
                return handleAgentNonStreamRequest(agent, conversationHistory, sessionId, userId);
            }

        } catch (Exception e) {
            log.error("Error handling Semantic Kernel request: {}", e.getMessage(), e);
            return Mono.just(createErrorResponse("Semantic Kernel processing failed: " + e.getMessage()));
        }
    }

    /**
     * 从模型名称中提取实际的 LLM 模型
     * 例如：agent@deepseek-chat → deepseek-chat
     */
    private String extractActualModel(String model) {
        if (model.contains("@")) {
            return model.split("@")[1];
        }

        // 如果没有指定具体模型，使用默认模型
        return agentFactory.getDefaultModelId();
    }

    /**
     * 构建完整的对话历史
     * 将所有消息合并为一个字符串，让 Agent 处理完整上下文
     */
    private String buildConversationHistory(ChatCompletionRequest request) {
        List<ChatMessage> messages = request.getMessages();
        if (messages == null || messages.isEmpty()) {
            return "你好，我需要什么帮助？";
        }

        StringBuilder conversation = new StringBuilder();
        for (ChatMessage message : messages) {
            String role = message.getRole();
            String content = message.getContent().toString();

            if ("system".equals(role)) {
                conversation.append("系统指令: ").append(content).append("\n\n");
            } else if ("user".equals(role)) {
                conversation.append("用户: ").append(content).append("\n\n");
            } else if ("assistant".equals(role)) {
                conversation.append("助手: ").append(content).append("\n\n");
            }
        }

        // 如果只有一条用户消息，直接返回内容
        if (messages.size() == 1 && "user".equals(messages.get(0).getRole())) {
            return messages.get(0).getContent().toString();
        }

        return conversation.toString().trim();
    }

    /**
     * 处理 Agent 非流式请求
     */
    private Mono<ChatCompletionResponse> handleAgentNonStreamRequest(ChatCompletionAgent agent, String userMessage,
            String sessionId, Long userId) {
        return agent.invokeAsync(userMessage)
                .map(result -> {
                    String assistantReply = result.getContent();
                    saveAssistantMessage(assistantReply, sessionId, userId, true);

                    ChatCompletionResponse response = new ChatCompletionResponse();
                    // ... 根据 AgentResult 构建 ChatCompletionResponse ...
                    return response;
                })
                .onErrorReturn(createErrorResponse("Agent invocation failed"));
    }

    private void saveAssistantMessage(String content, String sessionId, Long userId, boolean isNewSession) {
        if (content != null && !content.isEmpty()) {
            SaveMessageRequest saveMessageRequest = new SaveMessageRequest();
            saveMessageRequest.setSessionId(sessionId);
            saveMessageRequest.setSender(MessageSender.assistant);
            saveMessageRequest.setContent(content);
            chatSessionService.saveMessage(saveMessageRequest, userId);

            // 如果是新会话（第一轮对话），则触发标题生成
            if (isNewSession) {
                chatSessionService.generateAndSaveTitle(sessionId, userId);
            }
        }
    }

    /**
     * 处理 Agent 流式请求 - 使用 OpenAI 标准格式
     */
    private SseEmitter handleAgentStreamRequest(ChatCompletionAgent agent, String userMessage, String sessionId,
            Long userId) {
        SseEmitter emitter = new SseEmitter(TimeUnit.MINUTES.toMillis(5)); // 设置5分钟超时
        StringBuilder fullReply = new StringBuilder();

        // 设置错误和超时回调
        emitter.onError(throwable -> {
            log.error("SSE Agent stream error: {}", throwable.getMessage());
        });

        emitter.onTimeout(() -> {
            log.warn("SSE Agent stream timeout");
            try {
                emitter.send(SseEmitter.event().data("[DONE]"));
            } catch (Exception ignored) {
            }
            emitter.complete();
        });

        emitter.onCompletion(() -> {
            log.debug("SSE Agent stream completed.");
            // 注意：流式处理的最终消息保存现在依赖于 agent.invokeStreamAsync 的实现
            // 如果它提供了最终结果的回调或 CompletableFuture，则应在那里保存。
            // 暂时依赖 fullReply，但这可能不完全准确，取决于 agent 的实现。
            saveAssistantMessage(fullReply.toString(), sessionId, userId, true);
        });

        try {
            // 调用新的流式 Agent 方法，将 emitter 直接传递进去
            agent.invokeStreamAsync(userMessage, emitter)
                    .handle((result, throwable) -> {
                        if (throwable != null) {
                            log.error("Agent stream invocation failed: {}", throwable.getMessage());
                        } else if (result != null) {
                            // 在这里可以访问最终的 AgentResult，例如，如果需要记录完整的回复
                            fullReply.append(result.getContent());
                        }
                        return result;
                    });

        } catch (Exception e) {
            log.error("Failed to start Agent stream: {}", e.getMessage());
            try {
                String errorData = String.format(
                        "{\"error\":{\"message\":\"%s\",\"type\":\"agent_startup_error\",\"code\":\"agent_initialization_failed\"}}",
                        e.getMessage().replace("\"", "\\\""));
                emitter.send(SseEmitter.event().data(errorData));
                emitter.completeWithError(e);
            } catch (Exception sendError) {
                log.error("Failed to send startup error event: {}", sendError.getMessage());
            }
        }

        return emitter;
    }

    /**
     * 处理传统模型的流式请求
     */
    private SseEmitter handleStreamRequest(ChatCompletionRequest request, String sessionId, Long userId) {
        SseEmitter emitter = new SseEmitter(0L);
        StringBuilder fullReply = new StringBuilder();

        modelRouterService.routeAndStream(request)
                .doOnNext(chunk -> {
                    try {
                        if (chunk.getChoices() != null && !chunk.getChoices().isEmpty()) {
                            ChatCompletionChunk.Delta delta = chunk.getChoices().get(0).getDelta();
                            if (delta != null && delta.getContent() != null) {
                                fullReply.append(delta.getContent());
                            }
                        }
                        String json = objectMapper.writeValueAsString(chunk);
                        emitter.send(SseEmitter.event().data(json));
                    } catch (Exception e) {
                        log.error("Error sending SSE event", e);
                        emitter.completeWithError(e);
                    }
                })
                .doOnError(emitter::completeWithError)
                .doOnComplete(() -> {
                    try {
                        saveAssistantMessage(fullReply.toString(), sessionId, userId, true);
                        emitter.send(SseEmitter.event().data("[DONE]"));
                    } catch (Exception e) {
                        log.error("Error sending [DONE] event on completion", e);
                    }
                    emitter.complete();
                })
                .subscribe();

        return emitter;
    }

    /**
     * 创建错误响应
     */
    private ChatCompletionResponse createErrorResponse(String error) {
        ChatCompletionResponse response = new ChatCompletionResponse();
        // TODO: 设置错误信息
        return response;
    }

    /**
     * 获取可用模型列表 - 直接从ai_model_routes表查询所有字段
     */
    @GetMapping("/models")
    public Mono<ModelRouteResponse> getModels() {
        try {
            // 从数据库获取所有启用的模型路由配置，包含完整的关联信息
            List<AiModelRoute> routes = aiModelRouteRepository.findAllActiveRoutes();

            // 转换为DTO
            List<ModelRouteResponse.ModelRouteInfo> modelRouteInfos = routes.stream()
                    .map(ModelRouteResponse.ModelRouteInfo::fromEntity)
                    .collect(java.util.stream.Collectors.toList());

            log.debug("Retrieved {} model routes with complete information from ai_model_routes table",
                    modelRouteInfos.size());
            return Mono.just(new ModelRouteResponse(modelRouteInfos));

        } catch (Exception ex) {
            log.error("Error retrieving model routes from ai_model_routes table: {}", ex.getMessage(), ex);
            return Mono.error(new RuntimeException("Failed to retrieve model routes: " + ex.getMessage(), ex));
        }
    }

    /**
     * 图像生成 API
     * 兼容 OpenAI images/generations 接口
     */
    @PostMapping("/images/generations")
    public Mono<ImageGenerationResponse> imageGeneration(@RequestBody ImageGenerationRequest request,
            HttpServletRequest httpRequest) {
        log.info("Image generation request: model={}, prompt={}, size={}, quality={}, style={}",
                request.getModel(), request.getPrompt(), request.getSize(), request.getQuality(), request.getStyle());

        // 验证必要参数
        if (request.getPrompt() == null || request.getPrompt().trim().isEmpty()) {
            return Mono.error(new IllegalArgumentException("Prompt is required"));
        }

        // 设置默认模型
        if (request.getModel() == null || request.getModel().trim().isEmpty()) {
            request.setModel("dall-e-3");
        }

        // 设置默认尺寸
        if (request.getSize() == null || request.getSize().trim().isEmpty()) {
            if ("dall-e-3".equals(request.getModel())) {
                request.setSize("1024x1024");
            } else {
                request.setSize("1024x1024");
            }
        }

        // 设置默认质量和风格（仅适用于 DALL-E-3）
        if ("dall-e-3".equals(request.getModel())) {
            if (request.getQuality() == null || request.getQuality().trim().isEmpty()) {
                request.setQuality("standard");
            }
            if (request.getStyle() == null || request.getStyle().trim().isEmpty()) {
                request.setStyle("vivid");
            }
        }

        // 验证图像数量（DALL-E-3 只支持 n=1）
        if ("dall-e-3".equals(request.getModel()) && request.getN() != null && request.getN() > 1) {
            request.setN(1);
            log.warn("DALL-E-3 only supports n=1, adjusting request");
        }

        // 使用模型路由服务处理请求
        Long userId = userContextUtil.getCurrentUserId(httpRequest);
        return modelRouterService.imageGeneration(request, userId);
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Mono<String> health() {
        return Mono.just(String.format(
                "{\"status\":\"ok\",\"service\":\"kumhosunny-ai-proxy-with-semantic-kernel\",\"cached_agents\":%d}",
                agentFactory.getCachedAgentCount()));
    }

    /**
     * Semantic Kernel 统计信息
     */
    @GetMapping("/sk/stats")
    public Mono<String> semanticKernelStats() {
        return Mono.just(String.format(
                "{\"cached_agents\":%d,\"default_model\":\"%s\",\"framework\":\"Microsoft Semantic Kernel\"}",
                agentFactory.getCachedAgentCount(),
                agentFactory.getDefaultModelId()));
    }

    // --- 新增内部类用于解析 content JSON ---
    @Data
    private static class UserContent {
        private List<Part> parts;
        private List<Attachment> attachments;
    }

    @Data
    private static class Part {
        private String type;
        private String text;
        // 添加对 image_url 的支持，使用驼峰命名，Jackson会自动映射
        private Map<String, Object> image_url;
    }

    @Data
    private static class Attachment {
        private Long id;
        private String originalFileName;
        // 可以根据需要添加其他字段
    }

    /**
     * 处理附件、知识库查询和解析用户消息内容
     * <p>
     * 1. 解析所有用户消息的包装JSON格式content，转换为OpenAI标准多模态格式
     * 2. 检查会话历史中所有用户消息，查找附件
     * 3. 如果找到附件，提取问题文本和附件信息
     * 4. 对每个带附件的消息，使用 KnowledgePlugin 从向量数据库中查询相关信息
     * 5. 如果启用了知识库查询，从个人、部门、公司知识库中搜索相关信息
     * 6. 将所有查询结果汇总，格式化为一个系统提示，并注入到消息列表的开头
     *
     * @param request 聊天请求对象
     * @param userId  当前用户ID
     */
    private void processAttachmentsAndKnowledgeSearch(ChatCompletionRequest request, Long userId) {
        if (request.getMessages() == null || request.getMessages().isEmpty()) {
            return;
        }

        StringBuilder knowledgeResults = new StringBuilder();
        String knowledgePromptPrefix = "你是一个智能助手。请根据下面提供的" + '"' + "知识库参考资料" + '"';

        // 遍历所有消息，解析包装的content并转换为OpenAI标准格式
        for (ChatMessage message : request.getMessages()) {
            if ("user".equalsIgnoreCase(message.getRole()) && message.getContent() != null) {
                try {
                    String contentStr = message.getContent().toString();
                    // 尝试解析JSON格式的content
                    if (contentStr.trim().startsWith("{") &&
                            (contentStr.contains("\"parts\"") || contentStr.contains("\"attachments\""))) {

                        UserContent userContent = objectMapper.readValue(contentStr, UserContent.class);

                        // 转换为OpenAI标准多模态格式
                        List<Map<String, Object>> openAIContent = convertToOpenAIFormat(userContent);
                        message.setContent(openAIContent);

                        // 提取文本内容用于知识库查询
                        String question = extractQuestionFromParts(userContent.getParts());

                        log.debug("Converted user message to OpenAI multimodal format. Text: '{}'", question);

                        // 处理附件（如果存在）
                        if (userContent.getAttachments() != null && !userContent.getAttachments().isEmpty()) {
                            for (Attachment attachment : userContent.getAttachments()) {
                                // 跳过图片附件，不进行知识库查询
                                if (isImageFile(attachment.getOriginalFileName())) {
                                    log.debug("Skipping knowledge base search for image attachment: {} (ID: {})",
                                            attachment.getOriginalFileName(), attachment.getId());
                                    continue;
                                }

                                try {
                                    log.info("Searching specific file for question: '{}' with attachment: {} (ID: {})",
                                            question, attachment.getOriginalFileName(), attachment.getId());
                                    // 使用新的按文件ID搜索方法，只在当前附件文档中搜索
                                    String result = knowledgePlugin.searchKnowledgeByFileId(question,
                                            attachment.getId());
                                    if (result != null && !result.equals("在指定文件中未搜索到相关结果")) {
                                        knowledgeResults.append("**来自附件 ")
                                                .append(attachment.getOriginalFileName())
                                                .append(" 的搜索结果：**\n")
                                                .append(result).append("\n\n");
                                    }
                                } catch (Exception e) {
                                    log.error("Error searching in specific file for attachment ID {}: {}",
                                            attachment.getId(), e.getMessage(), e);
                                }
                            }
                        }
                    }
                } catch (IOException e) {
                    log.trace("Could not parse user message content as JSON, treating as plain text. Content: {}",
                            message.getContent().toString().length() > 100
                                    ? message.getContent().toString().substring(0, 100) + "..."
                                    : message.getContent().toString());
                    // 如果解析失败，保持原始content不变
                }
            }
        }
        // 获取最后一条用户消息作为搜索查询
        String lastUserMessage = getLastUserMessage(request);
        // 如果启用了知识库查询，从个人、部门、公司知识库中搜索相关信息
        if (Boolean.TRUE.equals(request.getKnowledgeEnabled()) && knowledgePlugin != null) {

            if (lastUserMessage != null && !lastUserMessage.isEmpty()) {
                log.info("Knowledge search enabled, searching in personal, department, and company knowledge bases");

                // 搜索个人知识库
                try {
                    String personalResult = knowledgePlugin.searchKnowledge(lastUserMessage, userId, "personal");
                    if (personalResult != null && !personalResult.equals("知识库未搜索到结果")) {
                        knowledgeResults.append("**个人知识库搜索结果：**\n")
                                .append(personalResult).append("\n\n");
                    }
                } catch (Exception e) {
                    log.error("Error searching personal knowledge base: {}", e.getMessage(), e);
                }
            }
        }

        // 先移除所有旧的知识库提示，以防重复
        request.getMessages().removeIf(m -> "system".equalsIgnoreCase(m.getRole()) &&
                m.getContent() != null && m.getContent().toString().startsWith(knowledgePromptPrefix));

        // 如果本次有新的查询结果，构建并注入新的系统提示
        if (knowledgeResults.length() > 0) {
            String finalKnowledgePrompt = knowledgePromptPrefix
                    + "来回答用户的问题。如果资料中的信息足够回答，请直接利用这些信息。如果资料不相关或不足以回答，请忽略资料并根据你的通用知识来回答。\n\n"
                    + "--- 知识库参考资料 ---\n"
                    + knowledgeResults.toString().trim();

            ChatMessage systemMessage = new ChatMessage("system", finalKnowledgePrompt);
            List<ChatMessage> messages = new ArrayList<>();
            messages.add(systemMessage);
            messages.addAll(request.getMessages());
            request.setMessages(messages);
            log.info("Injected knowledge base results into the prompt.");
        }
    }

    /**
     * 处理联网搜索
     * <p>
     * 1. 检查是否启用了联网搜索功能
     * 2. 如果启用，获取最后一条用户消息作为搜索查询
     * 3. 调用TavilyPlugin进行网络搜索
     * 4. 将搜索结果格式化并注入到系统提示中
     *
     * @param request 聊天请求对象
     * @param userId  当前用户ID
     */
    private void processWebSearch(ChatCompletionRequest request, Long userId) {
        // 检查是否启用了联网搜索
        if (!Boolean.TRUE.equals(request.getWebSearchEnabled()) || tavilyPlugin == null) {
            return;
        }

        // 获取最后一条用户消息作为搜索查询
        String searchQuery = getLastUserMessage(request);
        if (searchQuery == null || searchQuery.trim().isEmpty()) {
            log.warn("联网搜索已启用，但未找到有效的用户消息");
            return;
        }

        try {
            log.info("执行联网搜索，查询: {}", searchQuery);
            String searchResult = tavilyPlugin.searchWeb(searchQuery);

            if (searchResult != null && !searchResult.trim().isEmpty()) {
                // 解析搜索结果
                Map<String, Object> resultMap = objectMapper.readValue(searchResult,
                        new TypeReference<Map<String, Object>>() {
                        });

                // 检查是否有错误
                if (resultMap.containsKey("error")) {
                    log.error("联网搜索失败: {}", resultMap.get("error"));
                    return;
                }

                // 构建联网搜索结果的系统提示
                StringBuilder webSearchPrompt = new StringBuilder();
                webSearchPrompt.append("你是一个智能助手。我已经为你的查询进行了实时网络搜索，以下是搜索结果：\n\n");
                webSearchPrompt.append("【网络搜索结果】\n");
                webSearchPrompt.append("搜索查询: ").append(resultMap.get("query")).append("\n");

                // 添加AI生成的答案（如果有）
                if (resultMap.containsKey("answer") && resultMap.get("answer") != null) {
                    webSearchPrompt.append("AI总结答案: ").append(resultMap.get("answer")).append("\n\n");
                }

                // 添加搜索结果详情
                if (resultMap.containsKey("results")) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> results = (List<Map<String, Object>>) resultMap.get("results");
                    webSearchPrompt.append("详细搜索结果:\n");
                    for (int i = 0; i < results.size(); i++) {
                        Map<String, Object> result = results.get(i);
                        webSearchPrompt.append(String.format("%d. %s\n", i + 1, result.get("title")));
                        webSearchPrompt.append(String.format("   来源: %s\n", result.get("url")));
                        webSearchPrompt.append(String.format("   内容: %s\n\n", result.get("content")));
                    }
                }

                webSearchPrompt.append("请基于以上网络搜索结果来回答用户的问题。如果搜索结果相关且有用，请优先使用这些信息；如果搜索结果不够相关，请结合你的知识来回答。");

                // 移除之前的联网搜索系统提示（如果有）
                request.getMessages().removeIf(m -> "system".equalsIgnoreCase(m.getRole()) &&
                        m.getContent() != null && m.getContent().toString().contains("【网络搜索结果】"));

                // 注入新的联网搜索系统提示
                ChatMessage systemMessage = new ChatMessage("system", webSearchPrompt.toString());
                List<ChatMessage> messages = new ArrayList<>();
                messages.add(systemMessage);
                messages.addAll(request.getMessages());
                request.setMessages(messages);

                log.info("已将联网搜索结果注入到系统提示中");
            }
        } catch (Exception e) {
            log.error("联网搜索处理失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理附件、法律知识库查询和解析用户消息内容
     * <p>
     * 1. 解析所有用户消息的包装JSON格式content，转换为OpenAI标准多模态格式
     * 2. 检查会话历史中所有用户消息，查找附件
     * 3. 如果找到附件，提取问题文本和附件信息
     * 4. 对每个带附件的消息，使用 KnowledgePlugin 从向量数据库中查询相关信息
     * 5. 如果启用了知识库查询，从法律知识库中搜索相关信息
     * 6. 将所有查询结果汇总，格式化为一个系统提示，并注入到消息列表的开头
     *
     * @param request 聊天请求对象
     * @param userId  当前用户ID
     */
    private void processAttachmentsAndLeaglKnowledgeSearch(ChatCompletionRequest request, Long userId) {
        if (request.getMessages() == null || request.getMessages().isEmpty()) {
            return;
        }

        StringBuilder knowledgeResults = new StringBuilder();
        String knowledgePromptPrefix = "你是一个智能助手。请根据下面提供的" + '"' + "知识库参考资料" + '"';
        // 遍历所有消息，解析包装的content并转换为OpenAI标准格式
        for (ChatMessage message : request.getMessages()) {
            if ("user".equalsIgnoreCase(message.getRole()) && message.getContent() != null) {
                try {
                    String contentStr = message.getContent().toString();
                    // 尝试解析JSON格式的content
                    if (contentStr.trim().startsWith("{") &&
                            (contentStr.contains("\"parts\"") || contentStr.contains("\"attachments\""))) {

                        UserContent userContent = objectMapper.readValue(contentStr, UserContent.class);

                        // 转换为OpenAI标准多模态格式
                        List<Map<String, Object>> openAIContent = convertToOpenAIFormat(userContent);
                        message.setContent(openAIContent);

                        // 提取文本内容用于知识库查询
                        String question = extractQuestionFromParts(userContent.getParts());

                        log.debug("Converted user message to OpenAI multimodal format. Text: '{}'", question);

                        // 处理附件（如果存在）
                        if (userContent.getAttachments() != null && !userContent.getAttachments().isEmpty()) {
                            // 第一次对话查询文件全部内容信息
                            if (request.getMessages().size() == 1) {
                                for (Attachment attachment : userContent.getAttachments()) {
                                    // 跳过图片附件，不查询文件内容
                                    if (isImageFile(attachment.getOriginalFileName())) {
                                        log.debug("Skipping file content retrieval for image attachment: {} (ID: {})",
                                                attachment.getOriginalFileName(), attachment.getId());
                                        continue;
                                    }

                                    try {
                                        log.info(
                                                "Getting full content for first conversation with attachment: {} (ID: {})",
                                                attachment.getOriginalFileName(), attachment.getId());

                                        // 获取文件完整内容信息
                                        String fileContent = knowledgePlugin.getFileFullContent(attachment.getId());
                                        if (fileContent != null && !fileContent.equals("该文件暂无内容信息")) {
                                            knowledgeResults.append("**附件 ")
                                                    .append(attachment.getOriginalFileName())
                                                    .append(" 的内容信息：**\n")
                                                    .append(fileContent).append("\n\n");
                                        }
                                    } catch (Exception e) {
                                        log.error("Error getting full content for attachment ID {}: {}",
                                                attachment.getId(), e.getMessage(), e);
                                    }
                                }
                            }

                            for (Attachment attachment : userContent.getAttachments()) {
                                // 跳过图片附件，不进行知识库查询
                                if (isImageFile(attachment.getOriginalFileName())) {
                                    log.debug("Skipping knowledge base search for image attachment: {} (ID: {})",
                                            attachment.getOriginalFileName(), attachment.getId());
                                    continue;
                                }

                                try {
                                    log.info("Searching specific file for question: '{}' with attachment: {} (ID: {})",
                                            question, attachment.getOriginalFileName(), attachment.getId());
                                    // 使用新的按文件ID搜索方法，只在当前附件文档中搜索
                                    String result = knowledgePlugin.searchKnowledgeByFileId(question,
                                            attachment.getId());
                                    if (result != null && !result.equals("在指定文件中未搜索到相关结果")) {
                                        knowledgeResults.append("**来自附件 ")
                                                .append(attachment.getOriginalFileName())
                                                .append(" 的搜索结果：**\n")
                                                .append(result).append("\n\n");
                                    }
                                } catch (Exception e) {
                                    log.error("Error searching in specific file for attachment ID {}: {}",
                                            attachment.getId(), e.getMessage(), e);
                                }
                            }
                        }
                    }
                } catch (IOException e) {
                    log.trace("Could not parse user message content as JSON, treating as plain text. Content: {}",
                            message.getContent().toString().length() > 100
                                    ? message.getContent().toString().substring(0, 100) + "..."
                                    : message.getContent().toString());
                    // 如果解析失败，保持原始content不变
                }
            }
        }

        // 获取最后一条用户消息作为搜索查询
        String lastUserMessage = getLastUserMessage(request);
        // 搜索法律知识库
        try {
            // 第一次对话查询文件全部内容信息
            if (request.getMessages().size() > 1) {
                String legalResult = knowledgePlugin.searchKnowledge(lastUserMessage, userId, "legal");
                if (legalResult != null && !legalResult.equals("知识库未搜索到结果")) {
                    knowledgeResults.append("**法律知识库搜索结果：**\n")
                            .append(legalResult).append("\n\n");
                }
            }

        } catch (Exception e) {
            log.error("Error searching legal knowledge base: {}", e.getMessage(), e);
        }

        // 先移除所有旧的知识库提示，以防重复
        request.getMessages().removeIf(m -> "system".equalsIgnoreCase(m.getRole()) &&
                m.getContent() != null && m.getContent().toString().startsWith(knowledgePromptPrefix));

        // 如果本次有新的查询结果，构建并注入新的系统提示
        if (knowledgeResults.length() > 0) {
            String finalKnowledgePrompt = knowledgePromptPrefix
                    + "来回答用户的问题。如果资料中的信息足够回答，请直接利用这些信息。如果资料不相关或不足以回答，请忽略资料并根据你的通用知识来回答。\n\n"
                    + "--- 知识库参考资料 ---\n"
                    + knowledgeResults.toString().trim();

            ChatMessage systemMessage = new ChatMessage("system", finalKnowledgePrompt);
            List<ChatMessage> messages = new ArrayList<>();
            messages.add(systemMessage);
            messages.addAll(request.getMessages());
            request.setMessages(messages);
            log.info("Injected knowledge base results into the prompt.");
        }
    }

    /**
     * 将自定义格式转换为OpenAI标准多模态格式
     */
    private List<Map<String, Object>> convertToOpenAIFormat(UserContent userContent) {
        List<Map<String, Object>> result = new ArrayList<>();

        if (userContent.getParts() != null) {
            for (Part part : userContent.getParts()) {
                Map<String, Object> contentItem = new HashMap<>();

                if ("text".equalsIgnoreCase(part.getType())) {
                    contentItem.put("type", "text");
                    contentItem.put("text", part.getText());
                } else if ("image_url".equalsIgnoreCase(part.getType())) {
                    contentItem.put("type", "image_url");
                    // 从part中提取image_url信息
                    if (part.getImage_url() != null) {
                        contentItem.put("image_url", part.getImage_url());
                    }
                }

                if (!contentItem.isEmpty()) {
                    result.add(contentItem);
                }
            }
        }

        return result;
    }

    /**
     * 获取知识库类型
     */
    // private String getKnowledgeType(ChatCompletionRequest request) {
    // return request.getKnowledgeType() != null ? request.getKnowledgeType() :
    // "personal";
    // }

    /**
     * 安全地将 Object 转换为 String
     */
    private String contentToString(Object content) {
        if (content == null) {
            return null;
        }
        if (content instanceof String) {
            return (String) content;
        }
        return content.toString();
    }

    /**
     * 从 parts 列表中提取文本内容
     */
    private String extractQuestionFromParts(List<Part> parts) {
        if (parts == null || parts.isEmpty()) {
            return "";
        }
        return parts.stream()
                .filter(p -> "text".equalsIgnoreCase(p.getType()) && p.getText() != null)
                .map(Part::getText)
                .collect(Collectors.joining("\n"));
    }

    /**
     * 判断文件是否为图片类型
     */
    private boolean isImageFile(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return false;
        }

        String lowercaseFileName = fileName.toLowerCase();
        return lowercaseFileName.endsWith(".jpg") ||
                lowercaseFileName.endsWith(".jpeg") ||
                lowercaseFileName.endsWith(".png") ||
                lowercaseFileName.endsWith(".gif") ||
                lowercaseFileName.endsWith(".bmp") ||
                lowercaseFileName.endsWith(".webp") ||
                lowercaseFileName.endsWith(".svg") ||
                lowercaseFileName.endsWith(".ico") ||
                lowercaseFileName.endsWith(".tiff") ||
                lowercaseFileName.endsWith(".tif");
    }
}