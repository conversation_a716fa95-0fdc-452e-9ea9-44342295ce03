# KumhoSunny AI应用 - 主配置文件
spring:
  application:
    name: kumhosunny-ai-app
  profiles:
    active: dev  # 默认激活开发环境
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  # JPA配置 
  jpa:
    hibernate:
      ddl-auto: none  # 不进行数据库结构验证，使用现有表结构
    show-sql: false
    database-platform: org.hibernate.dialect.MySQL8Dialect
    properties:
      hibernate:
        format_sql: true
        use_sql_comments: true
    open-in-view: false
  data:
    redis:
      host: **************
      port: 6379
      password:
      database: 0
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 100MB
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: NON_NULL
  knife4j:
    enable: true
    openapi:
      title: KumhoSunny AI API文档
      description: 智能AI应用平台接口文档
      version: 0.0.1-SNAPSHOT
      contact:
        name: KumhoSunny
        email: <EMAIL>
      license:
        name: Apache 2.0
        url: https://www.apache.org/licenses/LICENSE-2.0.html

# 服务器配置
server:
  port: 3333
  servlet:
    context-path: /

# 应用信息
application:
  name: KumhoSunny AI
  version: 0.0.1-SNAPSHOT
  description: 智能AI应用平台

# 日志配置
logging:
  config: classpath:logback-spring.xml
  level:
    com.kumhosunny: DEBUG
    com.microsoft.semantickernel: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Qdrant向量数据库配置
qdrant:
  # Qdrant服务器地址
  host: ${QDRANT_HOST:**************}
  # Qdrant服务器端口
  port: ${QDRANT_PORT:6334}
  # 是否使用TLS加密
  use-tls: ${QDRANT_USE_TLS:false}
  # API密钥（如果需要认证）
  api-key: ${QDRANT_API_KEY:}
  # 连接超时（毫秒）
  timeout: ${QDRANT_TIMEOUT:30000}
  # 默认集合设置
  default:
    # 默认向量维度
    vector-size: ${QDRANT_DEFAULT_VECTOR_SIZE:1536}
    # 默认距离算法（Cosine, Euclid, Dot）
    distance: ${QDRANT_DEFAULT_DISTANCE:Cosine}
    # 默认搜索限制
    search-limit: ${QDRANT_DEFAULT_SEARCH_LIMIT:10}

# 应用自定义配置
tavily:
    api:
        key: "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"
app:
  jwt:
    secret: "mYx9EIDUCmjkPoM1Q1tAiLl+pMQT+y6vK/IG7d+9we43OhBXX0vG1D1wl6fI9eAueqNBl8u1gvz9V4nISJg=="
    expiration: 86400
  file:
    upload-path: ./uploads/
    max-size: 10485760
  ai:
    openai:
      api-key: ${OPENAI_API_KEY:}
      base-url: https://api.openai.com
      model: gpt-3.5-turbo
  # Embedding服务配置
  embedding:
    base-url: http://192.168.100.88:9997/v1
    api-key: sk-aaabbbcccdddeeefffggghhhiiijjjkkk
    model: bge-large-zh-v1.5
    dimension: 1024

  # 文学文档处理配置
  literature:
    chunk-size: 200
    default-collection: literature_collection_bge
    default-search-limit: 10
  # DuckDuckGo 搜索服务配置
  search:
    duckduckgo:
      # DuckDuckGo API 服务地址
      api-url: ${DUCKDUCKGO_API_URL:http://**************:6060}
      # 请求超时设置（毫秒）
      timeout: ${DUCKDUCKGO_TIMEOUT:30000}
      # 最大搜索结果数量
      max-results: ${DUCKDUCKGO_MAX_RESULTS:10}
      # 摘要最大长度
      summary-max-length: ${DUCKDUCKGO_SUMMARY_MAX_LENGTH:200}

  # 向量化处理线程池配置
  vectorization:
    thread-pool:
      # 核心线程数 - 建议设置为CPU核心数
      core-size: ${VECTORIZATION_CORE_SIZE:4}
      # 最大线程数 - 建议设置为CPU核心数的2倍
      max-size: ${VECTORIZATION_MAX_SIZE:8}
      # 队列容量 - 等待处理的任务队列大小
      queue-capacity: ${VECTORIZATION_QUEUE_CAPACITY:100}
      # 线程保活时间（秒）
      keep-alive-seconds: ${VECTORIZATION_KEEP_ALIVE:60}
    # 向量化批处理配置
    batch:
      # 批处理大小 - 每批次处理的文档分片数量
      size: ${VECTORIZATION_BATCH_SIZE:50}
      # 批处理超时时间（秒）
      timeout: ${VECTORIZATION_BATCH_TIMEOUT:300}

# N8n 工作流自动化配置
n8n:
  # API 基础地址
  base-url: ${N8N_BASE_URL:http://**************:15678}
  # API 访问令牌
  api-key: ${N8N_API_KEY:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJkMzYxMzZhOC0zM2U1LTQzMWUtODdiMS1jMjY1Y2Q4MjZiMzgiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUwMDU4NTE0fQ.5VUlnq-nnkMaZDk9Uklbz5Zf_W1pMG8OllI6WYNkcms}
  # 请求超时设置（秒）
  timeout: ${N8N_TIMEOUT:30}

dingtalk:
  appKey: dingb3ymimqlndb1zoqp
  appSecret: p_uY7uC8YoJGZAdCszW2mCzIMivh2rv5yEyccBnNPfzFteoBgYQ9LlBQc34Bi6dT
  callbackAddress: http://**************:3000/dingtalk-callback
#  callbackAddress:  http://ai.kumhosunny.cn/dingtalk-callback
#  callbackAddress: http://**************:9999/dingtalk-callback

document:
  parseUrl: http://**************:8100/general/v0/general


oss:
  endpoint: http://**************:9000
  access-key: admin
  secret-key: Kumhosunny9527
  bucketName: kumhosunnyai

# 阿里云OSS配置
aliyun:
  oss:
    # OSS访问域名
    endpoint: ${ALIYUN_OSS_ENDPOINT:https://oss-cn-shanghai.aliyuncs.com}
    # AccessKey ID
    access-key-id: ${ALIYUN_OSS_ACCESS_KEY_ID:LTAI5tC7AiJEwdoP61NgimHP}
    # AccessKey Secret
    access-key-secret: ${ALIYUN_OSS_ACCESS_KEY_SECRET:******************************}
    # Bucket名称
    bucket-name: ${ALIYUN_OSS_BUCKET_NAME:kumhosunny-ai}
    # 文件访问URL前缀（例如：https://your-bucket.oss-cn-hangzhou.aliyuncs.com）
    url-prefix: ${ALIYUN_OSS_URL_PREFIX:https://kumhosunny-ai.oss-cn-shanghai.aliyuncs.com}
    # 文件上传路径前缀
    path-prefix: ${ALIYUN_OSS_PATH_PREFIX:uploads/}