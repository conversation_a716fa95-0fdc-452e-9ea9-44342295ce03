package com.kumhosunny.chat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 图像编辑请求 DTO
 * 兼容 OpenAI Images API 格式和 Together.ai 格式
 */
@Data
public class ImageEditRequest {

    /**
     * 描述编辑图像的文本提示（必须）
     */
    private String prompt;

    /**
     * 要使用的模型ID
     * 例如: "black-forest-labs/FLUX.1-kontext-dev"
     */
    private String model;

    /**
     * 原始图像的URL（必须）
     * 支持 imageUrl 和 image_url 两种命名方式
     */
    @JsonProperty("image_url")
    private String imageUrl;

    /**
     * 图片URL的驼峰命名别名
     * 用于兼容前端的驼峰命名习惯
     */
    @JsonProperty("imageUrl")
    public void setImageUrlCamelCase(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    /**
     * 生成图像的数量 (1-10)
     */
    private Integer n = 1;

    /**
     * 图像尺寸
     */
    private String size;

    /**
     * 图像宽高比
     * "1:1", "16:9", "9:16", "4:3", "3:4"
     */
    private String aspectRatio;

    /**
     * 响应格式
     * "url" 或 "b64_json"
     */
    @JsonProperty("response_format")
    private String responseFormat = "url";

    /**
     * 图像质量
     * "standard" 或 "hd"
     */
    private String quality;

    /**
     * 图像风格
     * "natural" 或 "vivid"
     */
    private String style;

    /**
     * 用户标识
     */
    private String user;

    /**
     * 编辑强度 (0.0-1.0)
     * 控制编辑的强度，值越高编辑越明显
     */
    private Double strength;

    /**
     * 种子值，用于可重现的结果
     */
    private Integer seed;

    /**
     * 推理步数
     */
    private Integer steps;
}
