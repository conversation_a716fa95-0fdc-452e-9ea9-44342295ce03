package com.kumhosunny.chat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 图像生成请求 DTO
 * 兼容 OpenAI Images API 格式
 */
@Data
public class ImageGenerationRequest {

    /**
     * 描述生成图像的文本提示（必须）
     * 最大长度: 1000 characters for dall-e-2, 4000 characters for dall-e-3
     */
    private String prompt;

    /**
     * 要使用的模型ID
     * 例如: "dall-e-2", "dall-e-3"
     */
    private String model;

    /**
     * 生成图像的数量 (1-10)
     * 对于 dall-e-3，只支持 n=1
     */
    private Integer n = 1;

    /**
     * 图像尺寸
     * dall-e-2: "256x256", "512x512", "1024x1024"
     * dall-e-3: "1024x1024", "1792x1024", "1024x1792"
     */
    private String size;

    /**
     * 图像宽高比 (Vertex AI)
     * "1:1", "16:9", "9:16", "4:3", "3:4"
     */
    private String aspectRatio;

    /**
     * 响应格式
     * "url" 或 "b64_json"
     */
    @JsonProperty("response_format")
    private String responseFormat = "url";

    /**
     * 图像质量 (仅支持 dall-e-3)
     * "standard" 或 "hd"
     */
    private String quality;

    /**
     * 图像风格 (仅支持 dall-e-3)
     * "natural" 或 "vivid"
     */
    private String style;

    /**
     * 用户标识
     */
    private String user;

    /**
     * 图片URL (用于图片编辑)
     * 当进行图片编辑时，需要提供原始图片的URL
     */
    @JsonProperty("image_url")
    private String imageUrl;
}