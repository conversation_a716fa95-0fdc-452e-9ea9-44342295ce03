package com.kumhosunny.chat.service.provider.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kumhosunny.chat.dto.ChatCompletionChunk;
import com.kumhosunny.chat.dto.ChatCompletionRequest;
import com.kumhosunny.chat.dto.ChatCompletionResponse;
import com.kumhosunny.chat.dto.ImageGenerationRequest;
import com.kumhosunny.chat.dto.ImageGenerationResponse;
import com.kumhosunny.chat.dto.ImageEditRequest;
import com.kumhosunny.chat.dto.ImageEditResponse;
import com.kumhosunny.chat.service.provider.AIProviderService;
import com.kumhosunny.common.entity.AiProvider;
import com.kumhosunny.common.entity.AiGeneratedContent;
import com.kumhosunny.common.repository.AiGeneratedContentRepository;
import com.kumhosunny.common.util.OssUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;
import java.io.ByteArrayInputStream;
import java.io.InputStream;

/**
 * Together.ai AI 提供商服务实现
 * 专门处理 Together.ai 的图片生成和编辑功能
 */
@Service("togetherAIProvider")
public class TogetherAIProviderService implements AIProviderService {

    private static final Logger log = LoggerFactory.getLogger(TogetherAIProviderService.class);
    private static final String DEFAULT_BASE_URL = "https://api.together.xyz/v1";

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private AiGeneratedContentRepository aiGeneratedContentRepository;

    @Autowired
    private OssUtil ossUtil;

    private final HttpClient httpClient;
    private final WebClient webClient;

    public TogetherAIProviderService() {
        this.httpClient = HttpClient.newBuilder()
                .version(HttpClient.Version.HTTP_1_1)
                .connectTimeout(Duration.ofSeconds(10))
                .build();
        this.webClient = WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024)) // 10MB
                .build();
    }

    @Override
    public Mono<ChatCompletionResponse> chatCompletion(ChatCompletionRequest request, AiProvider provider) {
        log.debug("Using TogetherAI Provider for chat completion with provider: {}", provider.getProviderCode());
        if (!isAvailable(provider)) {
            return Mono.error(new IllegalStateException(
                    "Provider '" + provider.getProviderCode() + "' is not available or configured correctly."));
        }

        String baseUrl = provider.getBaseUrl() != null ? provider.getBaseUrl() : DEFAULT_BASE_URL;

        return Mono.fromCallable(() -> {
            try {
                String requestBody = objectMapper.writeValueAsString(request);
                log.info("TogetherAI Request URL: {}/chat/completions", baseUrl);
                log.info("TogetherAI Request Header: Authorization: Bearer {}",
                        provider.getApiKey().substring(0, Math.min(10, provider.getApiKey().length())) + "...");
                log.info("TogetherAI Request Body: {}", requestBody);

                HttpRequest httpRequest = HttpRequest.newBuilder()
                        .uri(URI.create(baseUrl + "/chat/completions"))
                        .header("Authorization", "Bearer " + provider.getApiKey())
                        .header("Content-Type", "application/json")
                        .header("User-Agent", "Kumhosunny-AI-App/1.0")
                        .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                        .timeout(Duration.ofSeconds(60))
                        .build();

                HttpResponse<String> response = httpClient.send(httpRequest, HttpResponse.BodyHandlers.ofString());

                if (response.statusCode() >= 400) {
                    log.error("API error with TogetherAI provider '{}' - Status: {}, Body: {}",
                            provider.getProviderCode(), response.statusCode(), response.body());
                    throw new RuntimeException(
                            "API error with TogetherAI provider '" + provider.getProviderCode() + "': "
                                    + response.body());
                }

                return objectMapper.readValue(response.body(), ChatCompletionResponse.class);

            } catch (IOException | InterruptedException e) {
                log.error("Error during HTTP request to TogetherAI provider '{}'", provider.getProviderCode(), e);
                throw new RuntimeException(
                        "HTTP request failed for TogetherAI provider '" + provider.getProviderCode() + "': "
                                + e.getMessage(),
                        e);
            }
        }).subscribeOn(Schedulers.boundedElastic());
    }

    @Override
    public Flux<ChatCompletionChunk> chatCompletionStream(ChatCompletionRequest request, AiProvider provider) {
        log.debug("Using TogetherAI Provider for streaming with provider: {}", provider.getProviderCode());
        request.setKnowledgeEnabled(null);
        if (!isAvailable(provider)) {
            return Flux.error(new IllegalStateException(
                    "Provider '" + provider.getProviderCode() + "' is not available or configured correctly."));
        }
        if (!Boolean.TRUE.equals(request.getStream())) {
            return Flux.error(new IllegalArgumentException("stream 参数必须为 true"));
        }

        String baseUrl = provider.getBaseUrl() != null ? provider.getBaseUrl() : DEFAULT_BASE_URL;

        return Mono.fromCallable(() -> {
            String requestBody = objectMapper.writeValueAsString(request);
            log.info("TogetherAI Stream Request URL: {}/chat/completions", baseUrl);
            log.info("TogetherAI Stream Request Header: Authorization: Bearer {}",
                    provider.getApiKey().substring(0, Math.min(10, provider.getApiKey().length())) + "...");
            log.info("TogetherAI Stream Request Body: {}", requestBody);

            HttpRequest httpRequest = HttpRequest.newBuilder()
                    .uri(URI.create(baseUrl + "/chat/completions"))
                    .header("Authorization", "Bearer " + provider.getApiKey())
                    .header("Content-Type", "application/json")
                    .header("Accept", "text/event-stream")
                    .header("User-Agent", "Kumhosunny-AI-App/1.0")
                    .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                    .timeout(Duration.ofMinutes(5))
                    .build();

            return httpClient.send(httpRequest, HttpResponse.BodyHandlers.ofLines());
        })
                .subscribeOn(Schedulers.boundedElastic())
                .flatMapMany(response -> {
                    if (response.statusCode() >= 400) {
                        String errorBody = response.body().collect(Collectors.joining("\n"));
                        log.error("API error during streaming with TogetherAI provider '{}' - Status: {}, Body: {}",
                                provider.getProviderCode(), response.statusCode(), errorBody);
                        return Flux.error(new RuntimeException(
                                "API error with TogetherAI provider '" + provider.getProviderCode() + "': "
                                        + errorBody));
                    }
                    return Flux.fromStream(response.body());
                })
                .map(line -> {
                    if (line.startsWith("data:")) {
                        return line.substring(5).trim();
                    }
                    return ""; // Return empty string for non-data lines to be filtered out later
                })
                .filter(data -> !data.isEmpty() && !"[DONE]".equals(data))
                .map(data -> {
                    try {
                        return objectMapper.readValue(data, ChatCompletionChunk.class);
                    } catch (Exception e) {
                        log.warn("Failed to parse chunk from TogetherAI provider '{}': {}", provider.getProviderCode(),
                                data, e);
                        return null; // Return null for parsing errors
                    }
                })
                .filter(Objects::nonNull);
    }

    @Override
    public String getProviderName() {
        return "Together.ai Provider";
    }

    @Override
    public boolean isAvailable() {
        return true;
    }

    /**
     * 图像生成
     */
    @Override
    public Mono<ImageGenerationResponse> imageGeneration(ImageGenerationRequest request, AiProvider provider,
            Long userId) {
        log.debug("Using TogetherAI Provider for image generation with provider: {}",
                provider.getProviderCode());
        if (!isAvailable(provider)) {
            return Mono.error(new IllegalStateException(
                    "Provider '" + provider.getProviderCode() + "' is not available or configured correctly."));
        }

        String baseUrl = provider.getBaseUrl() != null ? provider.getBaseUrl() : DEFAULT_BASE_URL;

        return Mono.fromCallable(() -> {
            try {
                // 构建 Together.ai 格式的请求
                Map<String, Object> togetherRequest = new HashMap<>();
                togetherRequest.put("model", request.getModel());
                togetherRequest.put("prompt", request.getPrompt());

                // 如果有图片URL，说明是图片编辑
                if (request.getImageUrl() != null && !request.getImageUrl().isEmpty()) {
                    togetherRequest.put("image_url", request.getImageUrl());
                }

                // 添加其他参数
                if (request.getN() != null) {
                    togetherRequest.put("n", request.getN());
                }
                if (request.getSize() != null) {
                    togetherRequest.put("size", request.getSize());
                }
                if (request.getQuality() != null) {
                    togetherRequest.put("quality", request.getQuality());
                }
                if (request.getStyle() != null) {
                    togetherRequest.put("style", request.getStyle());
                }

                String requestJson = objectMapper.writeValueAsString(togetherRequest);

                log.info("TogetherAI Image Generation Request URL: {}/images/generations", baseUrl);
                log.info("TogetherAI Image Generation Request Header: Authorization: Bearer {}",
                        provider.getApiKey().substring(0, Math.min(10, provider.getApiKey().length())) + "...");
                log.info("TogetherAI Image Generation Request Body: {}", requestJson);

                HttpRequest httpRequest = HttpRequest.newBuilder()
                        .uri(URI.create(baseUrl + "/images/generations"))
                        .header("Authorization", "Bearer " + provider.getApiKey())
                        .header("Content-Type", "application/json")
                        .header("User-Agent", "Kumhosunny-AI-App/1.0")
                        .POST(HttpRequest.BodyPublishers.ofString(requestJson))
                        .timeout(Duration.ofSeconds(120)) // 图像生成可能需要更长时间
                        .build();

                HttpResponse<String> response = httpClient.send(httpRequest, HttpResponse.BodyHandlers.ofString());

                if (response.statusCode() >= 400) {
                    log.error("API error during image generation with TogetherAI provider '{}' - Status: {}, Body: {}",
                            provider.getProviderCode(), response.statusCode(), response.body());
                    throw new RuntimeException(
                            "API error with TogetherAI provider '" + provider.getProviderCode() + "': "
                                    + response.body());
                }

                return objectMapper.readValue(response.body(), ImageGenerationResponse.class);

            } catch (IOException | InterruptedException e) {
                log.error("Error during image generation HTTP request to TogetherAI provider '{}'",
                        provider.getProviderCode(), e);
                Thread.currentThread().interrupt();
                throw new RuntimeException(
                        "Image generation HTTP request failed for TogetherAI provider '" + provider.getProviderCode()
                                + "': "
                                + e.getMessage(),
                        e);
            }
        }).subscribeOn(Schedulers.boundedElastic())
                .flatMap(response -> {
                    // 处理响应并保存图片
                    return processAndSaveImage(response, request, userId)
                            .map(newUrls -> {
                                for (int i = 0; i < response.getData().size(); i++) {
                                    response.getData().get(i).setUrl(newUrls.get(i));
                                    response.getData().get(i).setB64Json(null); // 清除b64数据，只保留URL
                                }
                                return response;
                            });
                });
    }

    /**
     * 图像编辑
     */
    @Override
    public Mono<ImageEditResponse> imageEdit(ImageEditRequest request, AiProvider provider, Long userId) {
        log.debug("Using TogetherAI Provider for image editing with provider: {}",
                provider.getProviderCode());
        if (!isAvailable(provider)) {
            return Mono.error(new IllegalStateException(
                    "Provider '" + provider.getProviderCode() + "' is not available or configured correctly."));
        }

        String baseUrl = provider.getBaseUrl() != null ? provider.getBaseUrl() : DEFAULT_BASE_URL;

        return Mono.fromCallable(() -> {
            try {
                // 构建 Together.ai 格式的请求
                Map<String, Object> togetherRequest = new HashMap<>();
                togetherRequest.put("model", request.getModel());
                togetherRequest.put("prompt", request.getPrompt());
                togetherRequest.put("image_url", request.getImageUrl());

                // 添加其他参数
                if (request.getN() != null) {
                    togetherRequest.put("n", request.getN());
                }
                if (request.getSize() != null) {
                    togetherRequest.put("size", request.getSize());
                }
                if (request.getQuality() != null) {
                    togetherRequest.put("quality", request.getQuality());
                }
                if (request.getStyle() != null) {
                    togetherRequest.put("style", request.getStyle());
                }
                if (request.getStrength() != null) {
                    togetherRequest.put("strength", request.getStrength());
                }
                if (request.getSeed() != null) {
                    togetherRequest.put("seed", request.getSeed());
                }
                if (request.getSteps() != null) {
                    togetherRequest.put("steps", request.getSteps());
                }

                String requestJson = objectMapper.writeValueAsString(togetherRequest);

                log.info("TogetherAI Image Edit Request URL: {}/images/generations", baseUrl);
                log.info("TogetherAI Image Edit Request Header: Authorization: Bearer {}",
                        provider.getApiKey().substring(0, Math.min(10, provider.getApiKey().length())) + "...");
                log.info("TogetherAI Image Edit Request Body: {}", requestJson);

                HttpRequest httpRequest = HttpRequest.newBuilder()
                        .uri(URI.create(baseUrl + "/images/generations"))
                        .header("Authorization", "Bearer " + provider.getApiKey())
                        .header("Content-Type", "application/json")
                        .header("User-Agent", "Kumhosunny-AI-App/1.0")
                        .POST(HttpRequest.BodyPublishers.ofString(requestJson))
                        .timeout(Duration.ofSeconds(120)) // 图像编辑可能需要更长时间
                        .build();

                HttpResponse<String> response = httpClient.send(httpRequest, HttpResponse.BodyHandlers.ofString());

                if (response.statusCode() >= 400) {
                    log.error("API error during image editing with TogetherAI provider '{}' - Status: {}, Body: {}",
                            provider.getProviderCode(), response.statusCode(), response.body());
                    throw new RuntimeException(
                            "API error with TogetherAI provider '" + provider.getProviderCode() + "': "
                                    + response.body());
                }

                return objectMapper.readValue(response.body(), ImageEditResponse.class);

            } catch (IOException | InterruptedException e) {
                log.error("Error during image editing HTTP request to TogetherAI provider '{}'",
                        provider.getProviderCode(), e);
                Thread.currentThread().interrupt();
                throw new RuntimeException(
                        "Image editing HTTP request failed for TogetherAI provider '" + provider.getProviderCode()
                                + "': "
                                + e.getMessage(),
                        e);
            }
        }).subscribeOn(Schedulers.boundedElastic())
                .flatMap(response -> {
                    // 处理响应并保存图片
                    return processAndSaveEditedImage(response, request, userId)
                            .map(newUrls -> {
                                for (int i = 0; i < response.getData().size(); i++) {
                                    response.getData().get(i).setUrl(newUrls.get(i));
                                    response.getData().get(i).setB64Json(null); // 清除b64数据，只保留URL
                                }
                                return response;
                            });
                });
    }

    /**
     * 将返回的图像数据（URL或Base64）处理并保存到OSS
     *
     * @param response 图像生成响应
     * @param request  原始请求
     * @param userId   用户ID
     * @return 包含新OSS URL的Mono列表
     */
    private Mono<List<String>> processAndSaveImage(ImageGenerationResponse response, ImageGenerationRequest request,
            Long userId) {

        List<Mono<String>> uploadMonos = response.getData().stream().map(image -> {
            if (image.getB64Json() != null && !image.getB64Json().isEmpty()) {
                // --- 处理 b64_json ---
                log.debug("Processing b64_json image data.");
                byte[] imageBytes = Base64.getDecoder().decode(image.getB64Json());
                Mono<InputStream> inputStreamMono = Mono.just(new ByteArrayInputStream(imageBytes));
                String originalFileName = "generated-" + UUID.randomUUID().toString() + ".png";
                return saveSingleImage(inputStreamMono, originalFileName, request, userId, image);

            } else if (image.getUrl() != null && image.getUrl().startsWith("http")) {
                // --- 处理 URL ---
                log.debug("Processing image from URL: {}", image.getUrl());
                String originalFileName = image.getUrl().substring(image.getUrl().lastIndexOf('/') + 1);
                // 使用WebClient下载图片
                Mono<InputStream> inputStreamMono = webClient.get()
                        .uri(image.getUrl())
                        .retrieve()
                        .bodyToMono(byte[].class)
                        .map(ByteArrayInputStream::new);
                return saveSingleImage(inputStreamMono, originalFileName, request, userId, image);

            } else {
                log.warn("Image data is empty for one of the results.");
                return Mono.just("error_or_empty_url");
            }
        }).collect(Collectors.toList());

        return Flux.merge(uploadMonos).collectList();
    }

    /**
     * 将返回的编辑图像数据（URL或Base64）处理并保存到OSS
     *
     * @param response 图像编辑响应
     * @param request  原始请求
     * @param userId   用户ID
     * @return 包含新OSS URL的Mono列表
     */
    private Mono<List<String>> processAndSaveEditedImage(ImageEditResponse response, ImageEditRequest request,
            Long userId) {

        List<Mono<String>> uploadMonos = response.getData().stream().map(image -> {
            if (image.getB64Json() != null && !image.getB64Json().isEmpty()) {
                // --- 处理 b64_json ---
                log.debug("Processing b64_json edited image data.");
                byte[] imageBytes = Base64.getDecoder().decode(image.getB64Json());
                Mono<InputStream> inputStreamMono = Mono.just(new ByteArrayInputStream(imageBytes));
                String originalFileName = "edited-" + UUID.randomUUID().toString() + ".png";
                return saveSingleEditedImage(inputStreamMono, originalFileName, request, userId, image);

            } else if (image.getUrl() != null && image.getUrl().startsWith("http")) {
                // --- 处理 URL ---
                log.debug("Processing edited image from URL: {}", image.getUrl());
                String originalFileName = image.getUrl().substring(image.getUrl().lastIndexOf('/') + 1);
                // 使用WebClient下载图片
                Mono<InputStream> inputStreamMono = webClient.get()
                        .uri(image.getUrl())
                        .retrieve()
                        .bodyToMono(byte[].class)
                        .map(ByteArrayInputStream::new);
                return saveSingleEditedImage(inputStreamMono, originalFileName, request, userId, image);

            } else {
                log.warn("Edited image data is empty for one of the results.");
                return Mono.just("error_or_empty_url");
            }
        }).collect(Collectors.toList());

        return Flux.merge(uploadMonos).collectList();
    }

    private Mono<String> saveSingleImage(Mono<InputStream> inputStreamMono, final String originalFileName,
            final ImageGenerationRequest request, final Long userId, final ImageGenerationResponse.ImageData image) {
        return inputStreamMono.flatMap(inputStream -> {
            try {
                String finalFileName = "generated-images/" + originalFileName;
                String imageUrl = ossUtil.uploadFile(inputStream, finalFileName, "image/png");

                // 保存生成内容记录
                AiGeneratedContent generatedContent = new AiGeneratedContent();
                generatedContent.setUserId(userId);
                generatedContent.setContentId(UUID.randomUUID().toString());
                generatedContent.setPrompt(request.getPrompt());
                generatedContent.setRevisedPrompt(image.getRevisedPrompt());
                generatedContent.setOssUrl(imageUrl);
                generatedContent.setContentType("image");
                generatedContent.setModelUsed(request.getModel());
                generatedContent.setSize(request.getAspectRatio());
                generatedContent.setStyle(request.getStyle());
                generatedContent.setQuality(request.getQuality());
                generatedContent.setResponseFormat(request.getResponseFormat());
                aiGeneratedContentRepository.save(generatedContent);

                return Mono.just(imageUrl);
            } catch (Exception e) {
                log.error("Failed to upload image to OSS", e);
                return Mono.error(e);
            }
        });
    }

    private Mono<String> saveSingleEditedImage(Mono<InputStream> inputStreamMono, final String originalFileName,
            final ImageEditRequest request, final Long userId, final ImageEditResponse.ImageData image) {
        return inputStreamMono.flatMap(inputStream -> {
            try {
                String finalFileName = "edited-images/" + originalFileName;
                String imageUrl = ossUtil.uploadFile(inputStream, finalFileName, "image/png");

                // 保存生成内容记录
                AiGeneratedContent generatedContent = new AiGeneratedContent();
                generatedContent.setUserId(userId);
                generatedContent.setContentId(UUID.randomUUID().toString());
                generatedContent.setPrompt(request.getPrompt());
                generatedContent.setRevisedPrompt(image.getRevisedPrompt());
                generatedContent.setOssUrl(imageUrl);
                generatedContent.setContentType("image_edit");
                generatedContent.setModelUsed(request.getModel());
                generatedContent.setSize(request.getAspectRatio());
                generatedContent.setStyle(request.getStyle());
                generatedContent.setQuality(request.getQuality());
                generatedContent.setResponseFormat(request.getResponseFormat());
                aiGeneratedContentRepository.save(generatedContent);

                return Mono.just(imageUrl);
            } catch (Exception e) {
                log.error("Failed to upload edited image to OSS", e);
                return Mono.error(e);
            }
        });
    }
}
