package com.kumhosunny.chat.service.impl;

import com.kumhosunny.chat.dto.ChatCompletionRequest;
import com.kumhosunny.chat.dto.ChatCompletionResponse;
import com.kumhosunny.chat.dto.ModelsResponse;
import com.kumhosunny.chat.dto.ImageGenerationRequest;
import com.kumhosunny.chat.dto.ImageGenerationResponse;
import com.kumhosunny.chat.dto.ImageEditRequest;
import com.kumhosunny.chat.dto.ImageEditResponse;
import com.kumhosunny.chat.service.ModelRouterService;
import com.kumhosunny.chat.service.provider.AIProviderService;
import com.kumhosunny.common.entity.AiModelRoute;
import com.kumhosunny.common.repository.AiModelRouteRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 模型路由服务实现 - 基于数据库动态配置
 * 
 * 特性:
 * 1. 从数据库动态读取模型路由配置
 * 2. 支持多租户、多环境
 * 3. 支持缓存以提高性能
 * 4. 支持路由条件匹配和优先级
 * 5. 支持故障转移等高级路由策略
 */
@Service
public class ModelRouterServiceImpl implements ModelRouterService {

    private static final Logger log = LoggerFactory.getLogger(ModelRouterServiceImpl.class);

    @Autowired
    private Map<String, AIProviderService> providerServices;

    @Autowired
    private AiModelRouteRepository modelRouteRepository;

    @Override
    public Mono<ChatCompletionResponse> routeAndProcess(ChatCompletionRequest request) {
        String model = request.getModel();

        if (model == null || model.trim().isEmpty()) {
            return Mono.error(new IllegalArgumentException("Model is required"));
        }

        try {
            // 从数据库获取路由配置
            Optional<AiModelRoute> routeOpt = findModelRoute(model);
            if (routeOpt.isEmpty()) {
                log.warn("No route found for model: {}", model);
                return Mono.error(new IllegalArgumentException("Unsupported model: " + model));
            }

            AiModelRoute route = routeOpt.get();
            var provider = route.getTargetModel().getProvider();
            String providerCode = provider.getProviderCode();

            // 获取提供商服务
            AIProviderService providerService = providerServices.get(providerCode);
            if (providerService == null) {
                log.warn("No specific provider service found for '{}', attempting to use default provider.",
                        providerCode);
                providerService = providerServices.get("defaultProvider");
                if (providerService == null) {
                    log.error("Default provider service is not available.");
                    return Mono.error(new IllegalStateException("Provider service not available: " + providerCode));
                }
            }

            // 检查提供商是否可用
            if (!providerService.isAvailable(provider)) {
                log.warn("Provider {} is not available for model: {}", providerCode, model);
                return Mono.error(new IllegalStateException("Provider " + providerCode + " is not available"));
            }

            log.debug("Routing model {} to provider {} via route: {}",
                    model, providerCode, route.getRouteName());

            // 调用提供商服务
            return providerService.chatCompletion(request, provider)
                    .doOnNext(response -> response.setModel(model)) // 确保返回的模型名称与请求一致
                    .doOnSuccess(response -> log.debug("Successfully processed request for model: {}", model))
                    .onErrorMap(ex -> {
                        log.error("Failed to process request with {} for model {}: {}",
                                providerCode, model, ex.getMessage(), ex);
                        return new RuntimeException(
                                "Failed to process request with " + providerCode + ": " + ex.getMessage(), ex);
                    });

        } catch (Exception ex) {
            log.error("Error in route processing for model {}: {}", model, ex.getMessage(), ex);
            return Mono.error(new RuntimeException("Route processing error: " + ex.getMessage(), ex));
        }
    }

    @Override
    @Cacheable(value = "availableModels", key = "#root.method.name")
    public Mono<ModelsResponse> getAvailableModels() {
        try {
            // 从数据库获取所有可用的模型路由
            List<AiModelRoute> routes = modelRouteRepository.findAllActiveRoutes();
            List<ModelsResponse.ModelInfo> models = new ArrayList<>();

            for (AiModelRoute route : routes) {
                if (route.getTargetModel() != null && route.getTargetModel().getProvider() != null) {
                    String ownedBy = "kumhosunny-" + route.getTargetModel().getProvider().getProviderCode();
                    models.add(new ModelsResponse.ModelInfo(route.getRequestModel(), ownedBy));
                }
            }

            log.debug("Retrieved {} available models from database", models.size());
            return Mono.just(new ModelsResponse(models));

        } catch (Exception ex) {
            log.error("Error retrieving available models: {}", ex.getMessage(), ex);
            return Mono.error(new RuntimeException("Failed to retrieve available models: " + ex.getMessage(), ex));
        }
    }

    @Override
    public boolean isModelAvailable(String modelId) {
        try {
            Optional<AiModelRoute> route = findModelRoute(modelId);
            boolean available = route.isPresent() &&
                    route.get().getTargetModel() != null &&
                    route.get().getTargetModel().getProvider() != null;
            log.debug("Model {} availability check: {}", modelId, available);
            return available;
        } catch (Exception ex) {
            log.error("Error checking model availability for {}: {}", modelId, ex.getMessage(), ex);
            return false;
        }
    }

    /**
     * 从数据库查找模型路由配置
     * 支持缓存以提高性能
     */
    private Optional<AiModelRoute> findModelRoute(String requestModel) {
        try {
            Optional<AiModelRoute> route = modelRouteRepository.findActiveRouteByRequestModel(requestModel);
            if (route.isPresent()) {
                log.debug("Found route for model {}: {} -> {}",
                        requestModel, route.get().getRouteName(),
                        route.get().getTargetModel() != null
                                ? route.get().getTargetModel().getProvider().getProviderCode()
                                : "unknown");
            }
            return route;
        } catch (Exception ex) {
            log.error("Error finding route for model {}: {}", requestModel, ex.getMessage(), ex);
            return Optional.empty();
        }
    }

    @Override
    public reactor.core.publisher.Flux<com.kumhosunny.chat.dto.ChatCompletionChunk> routeAndStream(
            ChatCompletionRequest request) {
        String model = request.getModel();

        if (model == null || model.trim().isEmpty()) {
            return reactor.core.publisher.Flux.error(new IllegalArgumentException("Model is required"));
        }

        try {
            Optional<com.kumhosunny.common.entity.AiModelRoute> routeOpt = findModelRoute(model);
            if (routeOpt.isEmpty()) {
                log.warn("No route found for model: {}", model);
                return reactor.core.publisher.Flux.error(new IllegalArgumentException("Unsupported model: " + model));
            }

            var route = routeOpt.get();
            var provider = route.getTargetModel().getProvider();
            String providerCode = provider.getProviderCode();
            AIProviderService providerService = providerServices.get(providerCode);
            if (providerService == null) {
                log.warn("No specific provider service found for '{}' (stream), attempting to use default provider.",
                        providerCode);
                providerService = providerServices.get("defaultProvider");
                if (providerService == null) {
                    log.error("Default provider service not available.");
                    return reactor.core.publisher.Flux
                            .error(new IllegalStateException("Provider service not available: " + providerCode));
                }
            }

            if (!providerService.isAvailable(provider)) {
                log.warn("Provider {} is not available for model: {}", providerCode, model);
                return reactor.core.publisher.Flux
                        .error(new IllegalStateException("Provider " + providerCode + " is not available"));
            }

            log.debug("Routing (stream) model {} to provider {} via route: {}", model, providerCode,
                    route.getRouteName());

            return providerService.chatCompletionStream(request, provider)
                    .doOnError(ex -> log.error("Failed to stream with {} for model {}: {}", providerCode, model,
                            ex.getMessage(), ex));

        } catch (Exception ex) {
            log.error("Error in stream route processing for model {}: {}", model, ex.getMessage(), ex);
            return reactor.core.publisher.Flux
                    .error(new RuntimeException("Route processing error: " + ex.getMessage(), ex));
        }
    }

    @Override
    public Mono<ImageGenerationResponse> imageGeneration(ImageGenerationRequest request, Long userId) {
        String model = request.getModel();

        if (model == null || model.trim().isEmpty()) {
            return Mono.error(new IllegalArgumentException("Model is required"));
        }

        try {
            // 从数据库获取路由配置
            Optional<AiModelRoute> routeOpt = findModelRoute(model);
            if (routeOpt.isEmpty()) {
                log.warn("No route found for image generation model: {}", model);
                return Mono.error(new IllegalArgumentException("Unsupported image generation model: " + model));
            }

            AiModelRoute route = routeOpt.get();
            var provider = route.getTargetModel().getProvider();
            String providerCode = provider.getProviderCode();

            // 获取提供商服务
            AIProviderService providerService = providerServices.get(providerCode);
            if (providerService == null) {
                log.warn("No specific provider service found for '{}', attempting to use default provider.",
                        providerCode);
                providerService = providerServices.get("defaultProvider");
                if (providerService == null) {
                    log.error("Default provider service is not available.");
                    return Mono.error(new IllegalStateException("Provider service not available: " + providerCode));
                }
            }

            // 检查提供商是否可用
            if (!providerService.isAvailable(provider)) {
                log.warn("Provider {} is not available for image generation model: {}", providerCode, model);
                return Mono.error(new IllegalStateException("Provider " + providerCode + " is not available"));
            }

            log.debug("Routing image generation model {} to provider {} via route: {}",
                    model, providerCode, route.getRouteName());

            // 调用提供商服务进行图像生成
            return providerService.imageGeneration(request, provider, userId)
                    .doOnSuccess(response -> log.debug("Successfully processed image generation request for model: {}",
                            model))
                    .onErrorMap(ex -> {
                        log.error("Failed to process image generation request with {} for model {}: {}",
                                providerCode, model, ex.getMessage(), ex);
                        return new RuntimeException(
                                "Failed to process image generation request with " + providerCode + ": "
                                        + ex.getMessage(),
                                ex);
                    });

        } catch (Exception ex) {
            log.error("Error in image generation route processing for model {}: {}", model, ex.getMessage(), ex);
            return Mono.error(new RuntimeException("Image generation route processing error: " + ex.getMessage(), ex));
        }
    }

    @Override
    public Mono<ImageEditResponse> imageEdit(ImageEditRequest request, Long userId) {
        String model = request.getModel();

        if (model == null || model.trim().isEmpty()) {
            return Mono.error(new IllegalArgumentException("Model is required"));
        }

        try {
            // 从数据库获取路由配置
            Optional<AiModelRoute> routeOpt = findModelRoute(model);
            if (routeOpt.isEmpty()) {
                log.warn("No route found for image editing model: {}", model);
                return Mono.error(new IllegalArgumentException("Unsupported image editing model: " + model));
            }

            AiModelRoute route = routeOpt.get();
            String providerCode = route.getTargetModel() != null && route.getTargetModel().getProvider() != null
                    ? route.getTargetModel().getProvider().getProviderCode()
                    : "defaultProvider";

            // 获取提供商服务
            AIProviderService providerService = providerServices.get(providerCode);
            if (providerService == null) {
                log.warn("No specific provider service found for '{}', attempting to use default provider.",
                        providerCode);
                providerService = providerServices.get("defaultProvider");
                if (providerService == null) {
                    log.error("Default provider service is not available.");
                    return Mono.error(new IllegalStateException("Provider service not available: " + providerCode));
                }
            }

            // 获取提供商配置
            com.kumhosunny.common.entity.AiProvider provider = route.getTargetModel() != null
                    ? route.getTargetModel().getProvider()
                    : null;
            if (provider == null) {
                log.error("Provider configuration not found for route: {}", route.getRouteName());
                return Mono.error(new IllegalStateException("Provider configuration not found"));
            }

            // 检查提供商是否可用
            if (!providerService.isAvailable(provider)) {
                log.warn("Provider {} is not available for image editing model: {}", providerCode, model);
                return Mono.error(new IllegalStateException("Provider " + providerCode + " is not available"));
            }

            log.debug("Routing image editing model {} to provider {} via route: {}",
                    model, providerCode, route.getRouteName());

            // 调用提供商服务进行图像编辑
            return providerService.imageEdit(request, provider, userId)
                    .doOnSuccess(response -> log.debug("Successfully processed image editing request for model: {}",
                            model))
                    .onErrorMap(ex -> {
                        log.error("Failed to process image editing request with {} for model {}: {}",
                                providerCode, model, ex.getMessage(), ex);
                        return new RuntimeException(
                                "Failed to process image editing request with " + providerCode + ": "
                                        + ex.getMessage(),
                                ex);
                    });

        } catch (Exception ex) {
            log.error("Error in image editing route processing for model {}: {}", model, ex.getMessage(), ex);
            return Mono.error(new RuntimeException("Image editing route processing error: " + ex.getMessage(), ex));
        }
    }
}