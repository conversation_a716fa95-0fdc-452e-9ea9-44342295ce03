package com.kumhosunny.tools.plugins;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TavilyPlugin测试类
 * 测试联网搜索功能
 */
public class TavilyPluginTest {

    private TavilyPlugin tavilyPlugin;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        tavilyPlugin = new TavilyPlugin();
        objectMapper = new ObjectMapper();
    }

    @Test
    void testWebSearchWithoutApiKey() {
        // 测试没有API密钥的情况
        String result = tavilyPlugin.webSearch("test query");

        assertNotNull(result);
        assertTrue(result.contains("error"));
        assertTrue(result.contains("Tavily API密钥未配置"));
    }

    @Test
    void testWebSearchResponseFormat() throws Exception {
        // 测试返回结果的JSON格式
        String result = tavilyPlugin.webSearch("test query");

        // 验证返回的是有效的JSON
        Map<String, Object> resultMap = objectMapper.readValue(result, new TypeReference<Map<String, Object>>() {
        });

        assertNotNull(resultMap);
        assertTrue(resultMap.containsKey("query"));
        assertEquals("test query", resultMap.get("query"));

        // 由于没有有效的API密钥，应该包含错误信息
        assertTrue(resultMap.containsKey("error"));
    }

    @Test
    void testSearchWebMethod() {
        // 测试简单的searchWeb方法
        String result = tavilyPlugin.searchWeb("Java programming");

        assertNotNull(result);
        // 应该返回JSON格式的字符串
        assertTrue(result.startsWith("{"));
        assertTrue(result.endsWith("}"));
    }

    @Test
    void testEmptyQuery() {
        // 测试空查询
        String result = tavilyPlugin.webSearch("");

        assertNotNull(result);
        assertTrue(result.contains("error"));
    }

    @Test
    void testNullQuery() {
        // 测试null查询
        String result = tavilyPlugin.webSearch(null);

        assertNotNull(result);
        assertTrue(result.contains("error"));
    }

    /**
     * 手动测试方法（需要真实的API密钥）
     * 这个方法不会在自动化测试中运行，需要手动执行
     */
    // @Test
    void manualTestWithRealApiKey() {
        // 注意：这个测试需要真实的Tavily API密钥
        // 在application.properties中设置: tavily.api.key=your-real-api-key

        String result = tavilyPlugin.webSearch("What is artificial intelligence?");
        System.out.println("Search result: " + result);

        try {
            Map<String, Object> resultMap = objectMapper.readValue(result, new TypeReference<Map<String, Object>>() {
            });

            // 验证成功响应的结构
            assertNotNull(resultMap);
            assertTrue(resultMap.containsKey("query"));
            assertFalse(resultMap.containsKey("error"));

            if (resultMap.containsKey("answer")) {
                System.out.println("AI Answer: " + resultMap.get("answer"));
            }

            if (resultMap.containsKey("results")) {
                System.out.println("Search results count: " +
                        ((java.util.List<?>) resultMap.get("results")).size());
            }

        } catch (Exception e) {
            fail("Failed to parse search result JSON: " + e.getMessage());
        }
    }
}
