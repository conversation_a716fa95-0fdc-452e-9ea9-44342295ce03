package com.kumhosunny.tools.plugins;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.RestClientException;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import com.microsoft.semantickernel.semanticfunctions.annotations.DefineKernelFunction;
import com.microsoft.semantickernel.semanticfunctions.annotations.KernelFunctionParameter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.time.LocalDateTime;

/**
 * Tavily联网搜索插件
 * 提供智能网络搜索功能，使用Tavily.com API进行实时网络搜索
 * 
 * <AUTHOR>
 */
@Component
public class TavilyPlugin {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    
    @Value("${tavily.api.key:}")
    private String apiKey;
    
    @Value("${tavily.api.url:https://api.tavily.com}")
    private String apiUrl;
    
    @Value("${tavily.search.max-results:5}")
    private int maxResults;
    
    @Value("${tavily.search.timeout:30000}")
    private int timeout;

    /**
     * 构造函数
     */
    public TavilyPlugin() {
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 网络搜索
     * 
     * @param query 搜索查询
     * @return JSON格式的搜索结果数据
     */
    @DefineKernelFunction(name = "webSearch", description = "执行网络搜索并返回JSON格式的结构化搜索结果。返回的数据包含查询词、搜索结果列表、答案等信息，需要AI解析并以用户友好的方式呈现给用户")
    public String webSearch(
            @KernelFunctionParameter(name = "query", description = "搜索查询字符串") String query) {

        try {
            if (apiKey == null || apiKey.trim().isEmpty()) {
                return createErrorResponse("Tavily API密钥未配置", query);
            }

            TavilySearchResponse response = performSearch(query);
            if (response == null) {
                return createErrorResponse("未获取到有效响应", query);
            }

            return buildSearchResultJson(response, query);

        } catch (RestClientException e) {
            return createErrorResponse("网络连接错误 - " + e.getMessage(), query);
        } catch (Exception e) {
            return createErrorResponse("搜索异常 - " + e.getMessage(), query);
        }
    }

    /**
     * 执行搜索请求
     */
    private TavilySearchResponse performSearch(String query) {
        String url = apiUrl + "/search";
        
        // 构建请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("query", query);
        requestBody.put("max_results", maxResults);
        requestBody.put("include_answer", true);
        requestBody.put("include_raw_content", false);
        requestBody.put("search_depth", "basic");
        requestBody.put("topic", "general");

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(apiKey);

        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

        try {
            ResponseEntity<TavilySearchResponse> response = restTemplate.exchange(
                url, HttpMethod.POST, entity, TavilySearchResponse.class);
            return response.getBody();
        } catch (Exception e) {
            throw new RestClientException("Tavily API调用失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建搜索结果JSON
     */
    private String buildSearchResultJson(TavilySearchResponse response, String query)
            throws JsonProcessingException {

        if (response.getResults() == null || response.getResults().isEmpty()) {
            return createErrorResponse("未找到相关结果", query);
        }

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("query", query);
        resultMap.put("answer", response.getAnswer());
        resultMap.put("total_results", response.getResults().size());
        resultMap.put("search_time", LocalDateTime.now().toString());
        resultMap.put("response_time", response.getResponseTime());

        List<Map<String, Object>> results = new ArrayList<>();
        for (TavilySearchResult result : response.getResults()) {
            Map<String, Object> resultItem = new HashMap<>();
            resultItem.put("title", getValueOrDefault(result.getTitle(), "无标题"));
            resultItem.put("url", getValueOrDefault(result.getUrl(), ""));
            resultItem.put("content", getValueOrDefault(result.getContent(), ""));
            resultItem.put("score", result.getScore());
            results.add(resultItem);
        }
        resultMap.put("results", results);

        return objectMapper.writeValueAsString(resultMap);
    }

    /**
     * 创建错误响应
     */
    private String createErrorResponse(String error, String query) {
        try {
            Map<String, String> errorMap = new HashMap<>();
            errorMap.put("error", error);
            errorMap.put("query", query);
            return objectMapper.writeValueAsString(errorMap);
        } catch (JsonProcessingException e) {
            return String.format("{\"error\": \"%s\", \"query\": \"%s\"}", error, query);
        }
    }

    /**
     * 获取值或默认值
     */
    private String getValueOrDefault(String value, String defaultValue) {
        return value != null ? value : defaultValue;
    }

    /**
     * 简单的网络搜索方法，不使用Semantic Kernel注解
     * 供ModelProxyController直接调用
     */
    public String searchWeb(String query) {
        return webSearch(query);
    }

    // 内部类：Tavily搜索响应
    @Data
    public static class TavilySearchResponse {
        private String query;
        private String answer;
        private List<TavilySearchResult> results;
        @JsonProperty("response_time")
        private String responseTime;
    }

    // 内部类：Tavily搜索结果
    @Data
    public static class TavilySearchResult {
        private String title;
        private String url;
        private String content;
        private Double score;
        @JsonProperty("raw_content")
        private String rawContent;
    }
}
