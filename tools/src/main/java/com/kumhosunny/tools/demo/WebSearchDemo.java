package com.kumhosunny.tools.demo;

import com.kumhosunny.tools.plugins.TavilyPlugin;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

import java.util.Map;
import java.util.List;

/**
 * 联网搜索功能演示
 * 展示如何使用TavilyPlugin进行网络搜索
 */
public class WebSearchDemo {

    public static void main(String[] args) {
        System.out.println("=== Tavily联网搜索功能演示 ===\n");
        
        // 创建TavilyPlugin实例
        TavilyPlugin tavilyPlugin = new TavilyPlugin();
        ObjectMapper objectMapper = new ObjectMapper();
        
        // 测试查询列表
        String[] testQueries = {
            "What is artificial intelligence?",
            "Latest news about quantum computing",
            "How to use Java Spring Boot?",
            "Weather in Beijing today",
            "最新的人工智能发展"
        };
        
        for (String query : testQueries) {
            System.out.println("🔍 搜索查询: " + query);
            System.out.println("─".repeat(50));
            
            try {
                // 执行搜索
                String result = tavilyPlugin.searchWeb(query);
                
                // 解析结果
                Map<String, Object> resultMap = objectMapper.readValue(result, new TypeReference<Map<String, Object>>() {});
                
                // 显示结果
                if (resultMap.containsKey("error")) {
                    System.out.println("❌ 搜索失败: " + resultMap.get("error"));
                } else {
                    System.out.println("✅ 搜索成功!");
                    
                    // 显示AI生成的答案
                    if (resultMap.containsKey("answer") && resultMap.get("answer") != null) {
                        System.out.println("\n📝 AI总结答案:");
                        System.out.println(resultMap.get("answer"));
                    }
                    
                    // 显示搜索结果
                    if (resultMap.containsKey("results")) {
                        @SuppressWarnings("unchecked")
                        List<Map<String, Object>> results = (List<Map<String, Object>>) resultMap.get("results");
                        
                        System.out.println("\n📋 搜索结果 (" + results.size() + " 条):");
                        for (int i = 0; i < results.size(); i++) {
                            Map<String, Object> item = results.get(i);
                            System.out.println("\n" + (i + 1) + ". " + item.get("title"));
                            System.out.println("   🔗 " + item.get("url"));
                            System.out.println("   📄 " + item.get("content"));
                            if (item.containsKey("score")) {
                                System.out.println("   ⭐ 相关度: " + item.get("score"));
                            }
                        }
                    }
                    
                    // 显示响应时间
                    if (resultMap.containsKey("response_time")) {
                        System.out.println("\n⏱️ 响应时间: " + resultMap.get("response_time") + "秒");
                    }
                }
                
            } catch (Exception e) {
                System.out.println("❌ 处理搜索结果时出错: " + e.getMessage());
            }
            
            System.out.println("\n" + "=".repeat(80) + "\n");
        }
        
        // 展示如何在ModelProxyController中使用
        System.out.println("📚 在ModelProxyController中的使用示例:");
        System.out.println("─".repeat(50));
        System.out.println("1. 在ChatCompletionRequest中设置 webSearchEnabled = true");
        System.out.println("2. processWebSearch方法会自动调用TavilyPlugin");
        System.out.println("3. 搜索结果会被格式化并注入到系统提示中");
        System.out.println("4. AI模型会基于搜索结果回答用户问题");
        
        System.out.println("\n📋 请求示例:");
        System.out.println("{");
        System.out.println("  \"model\": \"deepseek-ai/DeepSeek-V3\",");
        System.out.println("  \"webSearchEnabled\": true,");
        System.out.println("  \"messages\": [");
        System.out.println("    {");
        System.out.println("      \"role\": \"user\",");
        System.out.println("      \"content\": \"What are the latest AI developments?\"");
        System.out.println("    }");
        System.out.println("  ]");
        System.out.println("}");
        
        System.out.println("\n🔧 配置说明:");
        System.out.println("─".repeat(50));
        System.out.println("在application.properties中配置:");
        System.out.println("tavily.api.key=your-tavily-api-key");
        System.out.println("tavily.api.url=https://api.tavily.com");
        System.out.println("tavily.search.max-results=5");
        System.out.println("tavily.search.timeout=30000");
        
        System.out.println("\n✨ 功能特点:");
        System.out.println("─".repeat(50));
        System.out.println("• 实时网络搜索");
        System.out.println("• AI生成的答案摘要");
        System.out.println("• 结构化搜索结果");
        System.out.println("• 自动错误处理");
        System.out.println("• 可配置的搜索参数");
        System.out.println("• 与现有聊天流程无缝集成");
        
        System.out.println("\n🎉 联网搜索功能演示完成!");
    }
}
